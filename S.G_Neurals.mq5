//+------------------------------------------------------------------+
//|                                                   S.G_neurals.mq5 |
//|                                    STABILITY GWIDIBIRA Neural EA |
//|                                 https://www.mql5.com/en/users/sg |
//+------------------------------------------------------------------+
#property copyright "STABILITY GWIDIBIRA"
#property link      "https://www.mql5.com/en/users/sg"
#property version   "2.00"
#property description "Neural Network XAUUSD Trading EA"
#property description "Uses trained ML models for price prediction"
#property description "Simplified version - ML predictions only"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Windows API for executing system commands
#import "kernel32.dll"
   int WinExec(string lpCmdLine, int uCmdShow);
#import

//--- Trading objects
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;

//--- Input parameters
input group "=== NEURAL NETWORK SETTINGS ==="
input string   PythonPath = "python";                    // Python executable path
input string   ScriptPath = "predict_realtime.py";       // Prediction script path
input double   SignalThreshold = 0.5;                    // Prediction threshold for signals
input double   ConfidenceThreshold = 1.5;                // Minimum confidence for trading
input int      PredictionInterval = 60;                  // Prediction interval in seconds

input group "=== RISK MANAGEMENT ==="
input double   RiskPercent = 1.0;                        // Risk per trade (% of account)
input double   StopLossPoints = 50.0;                    // Stop loss in points
input double   TakeProfitPoints = 100.0;                 // Take profit in points
input double   MinLotSize = 0.01;                        // Minimum lot size
input double   MaxLotSize = 1.0;                         // Maximum lot size

input group "=== TRADING SETTINGS ==="
input bool     EnableLongTrades = true;                  // Enable long trades
input bool     EnableShortTrades = true;                 // Enable short trades
input int      MaxPositions = 1;                         // Maximum concurrent positions
input int      MagicNumber = 123456;                     // Magic number for trades
input string   TradeComment = "SG_Neural_ML";            // Trade comment

input group "=== TIME FILTERS ==="
input bool     UseTimeFilter = true;                     // Enable time filter
input int      StartHour = 1;                            // Trading start hour (server time)
input int      EndHour = 23;                             // Trading end hour (server time)

//--- Global variables
datetime       lastPredictionTime = 0;
double         lastPrediction = 0.0;
string         lastSignal = "HOLD";
double         lastConfidence = 0.0;
bool           modelLoaded = false;
double         dailyProfit = 0.0;
datetime       lastTradeTime = 0;

//--- File handles for Python communication
string         dataFileName = "mt5_data.csv";
string         predictionFileName = "prediction_result.txt";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== S.G Neural EA Initialization ===");

   // Set trade parameters
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(Symbol());

   // Test Python integration
   if(!TestPythonIntegration())
   {
      Print("ERROR: Python integration test failed - EA cannot function without ML model");
      return INIT_FAILED;
   }
   else
   {
      Print("SUCCESS: Python ML model integration working");
      modelLoaded = true;
   }

   Print("=== S.G Neural EA Initialized Successfully ===");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("=== S.G Neural EA Deinitialization ===");

   // Clean up temporary files
   FileDelete(dataFileName);
   FileDelete(predictionFileName);

   Print("EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Update daily profit tracking
   UpdateDailyProfit();

   // Check time filters
   if(!IsTimeToTrade())
      return;

   // Get ML prediction based on interval
   if((TimeCurrent() - lastPredictionTime) >= PredictionInterval)
   {
      if(modelLoaded)
      {
         GetMLPrediction();
      }
      else
      {
         Print("ERROR: ML model not loaded - EA cannot function");
         return;
      }
   }

   // Execute trading logic based on ML predictions
   ExecuteTradingLogic();

   // Display information on chart
   DisplayInfo();
}

//+------------------------------------------------------------------+
//| Test Python ML model integration                                |
//+------------------------------------------------------------------+
bool TestPythonIntegration()
{
   Print("Testing Python ML model integration...");

   // Create test data file with recent market data
   if(!CreateDataFile(100))
   {
      Print("ERROR: Failed to create test data file for ML model");
      return false;
   }

   // Execute Python ML prediction script
   string command = PythonPath + " " + ScriptPath;
   int result = WinExec(command, 0); // 0 = SW_HIDE (hidden window)

   if(result <= 31)
   {
      Print("ERROR: Python ML script execution failed. Return code: ", result);
      return false;
   }

   // Wait for ML model to process and generate prediction
   Sleep(3000);

   // Check if ML prediction file was created
   if(!FileIsExist(predictionFileName))
   {
      Print("ERROR: ML prediction file not created");
      return false;
   }

   // Try to read ML prediction result
   string predictionResult = ReadPredictionFile();
   if(predictionResult == "")
   {
      Print("ERROR: Failed to read ML prediction result");
      return false;
   }

   Print("Python ML model integration successful. Result: ", predictionResult);
   return true;
}

//+------------------------------------------------------------------+
//| Create data file for Python script                              |
//+------------------------------------------------------------------+
bool CreateDataFile(int bars = 100)
{
   int fileHandle = FileOpen(dataFileName, FILE_WRITE | FILE_CSV | FILE_ANSI);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create data file");
      return false;
   }

   // Write CSV header
   FileWrite(fileHandle, "datetime,open,high,low,close,tick_volume,volume,spread");

   // Write OHLC data
   for(int i = bars - 1; i >= 0; i--)
   {
      datetime time = iTime(Symbol(), PERIOD_M10, i);
      double open = iOpen(Symbol(), PERIOD_M10, i);
      double high = iHigh(Symbol(), PERIOD_M10, i);
      double low = iLow(Symbol(), PERIOD_M10, i);
      double close = iClose(Symbol(), PERIOD_M10, i);
      long tickVolume = iTickVolume(Symbol(), PERIOD_M10, i);
      long volume = iRealVolume(Symbol(), PERIOD_M10, i);
      int spread = (int)SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);

      string timeStr = TimeToString(time, TIME_DATE | TIME_MINUTES);
      FileWrite(fileHandle, timeStr, open, high, low, close, tickVolume, volume, spread);
   }

   FileClose(fileHandle);
   return true;
}

//+------------------------------------------------------------------+
//| Get ML model prediction                                          |
//+------------------------------------------------------------------+
void GetMLPrediction()
{
   Print("Getting ML prediction...");

   // Create fresh data file with latest market data
   if(!CreateDataFile(100))
   {
      Print("ERROR: Failed to create data file for ML prediction");
      return;
   }

   // Execute Python ML prediction script
   string command = PythonPath + " " + ScriptPath;
   int result = WinExec(command, 0); // 0 = SW_HIDE (hidden window)

   if(result <= 31)
   {
      Print("ERROR: Python ML prediction failed. Return code: ", result);
      return;
   }

   // Wait for ML model to process
   Sleep(2000);

   // Read ML prediction result
   string predictionResult = ReadPredictionFile();
   if(predictionResult != "")
   {
      ParsePredictionResult(predictionResult);
      lastPredictionTime = TimeCurrent();
      Print("ML Prediction: ", lastPrediction, " Signal: ", lastSignal, " Confidence: ", lastConfidence);
   }
   else
   {
      Print("WARNING: No ML prediction result received");
   }
}

//+------------------------------------------------------------------+
//| Read prediction result from file                                 |
//+------------------------------------------------------------------+
string ReadPredictionFile()
{
   int fileHandle = FileOpen(predictionFileName, FILE_READ | FILE_TXT | FILE_ANSI);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to open prediction file");
      return "";
   }

   string result = "";
   if(!FileIsEnding(fileHandle))
   {
      result = FileReadString(fileHandle);
   }

   FileClose(fileHandle);
   return result;
}

//+------------------------------------------------------------------+
//| Parse prediction result string                                   |
//+------------------------------------------------------------------+
void ParsePredictionResult(string result)
{
   // Expected format: "SIGNAL:BUY,PREDICTION:0.5432,CONFIDENCE:1.8"
   string parts[];
   int count = StringSplit(result, ',', parts);

   for(int i = 0; i < count; i++)
   {
      string keyValue[];
      if(StringSplit(parts[i], ':', keyValue) == 2)
      {
         if(keyValue[0] == "SIGNAL")
         {
            lastSignal = keyValue[1];
         }
         else if(keyValue[0] == "PREDICTION")
         {
            lastPrediction = StringToDouble(keyValue[1]);
         }
         else if(keyValue[0] == "CONFIDENCE")
         {
            lastConfidence = StringToDouble(keyValue[1]);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Execute trading logic based on ML predictions                   |
//+------------------------------------------------------------------+
void ExecuteTradingLogic()
{
   // Only trade if we have a valid ML signal with sufficient confidence
   if(lastSignal == "HOLD" || lastConfidence < ConfidenceThreshold)
   {
      Print("No valid ML signal. Signal: ", lastSignal, " Confidence: ", lastConfidence);
      return;
   }

   // Check position limits
   if(PositionsTotal() >= MaxPositions)
   {
      Print("Maximum positions reached: ", PositionsTotal());
      return;
   }

   // Check if we already have a position for this symbol
   if(position.Select(Symbol()))
   {
      Print("Position already exists for ", Symbol());
      return;
   }

   // Calculate position size based on risk management
   double lotSize = CalculateLotSize();
   if(lotSize <= 0)
   {
      Print("Invalid lot size calculated: ", lotSize);
      return;
   }

   // Calculate stop loss and take profit levels
   double stopLoss = CalculateStopLoss();
   double takeProfit = CalculateTakeProfit();

   // Execute trade based on ML signal
   if(lastSignal == "BUY" && EnableLongTrades)
   {
      ExecuteBuyOrder(lotSize, stopLoss, takeProfit);
   }
   else if(lastSignal == "SELL" && EnableShortTrades)
   {
      ExecuteSellOrder(lotSize, stopLoss, takeProfit);
   }
   else
   {
      Print("Trade direction disabled or invalid signal: ", lastSignal);
   }
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk percentage                |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * RiskPercent / 100.0;

   // Simple calculation based on stop loss points
   double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
   double lotSize = riskAmount / (StopLossPoints * tickValue);

   // Apply lot size limits
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(lotSize, MinLotSize);
   lotSize = MathMin(lotSize, MaxLotSize);
   lotSize = MathMin(lotSize, maxLot);
   lotSize = MathMax(lotSize, minLot);

   // Normalize to lot step
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   Print("Calculated lot size: ", lotSize, " Risk amount: ", riskAmount);
   return lotSize;
}

//+------------------------------------------------------------------+
//| Calculate stop loss price                                        |
//+------------------------------------------------------------------+
double CalculateStopLoss()
{
   double pointValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   if(lastSignal == "BUY")
   {
      double askPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      return askPrice - (StopLossPoints * pointValue);
   }
   else if(lastSignal == "SELL")
   {
      double bidPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      return bidPrice + (StopLossPoints * pointValue);
   }

   return 0;
}

//+------------------------------------------------------------------+
//| Calculate take profit price                                      |
//+------------------------------------------------------------------+
double CalculateTakeProfit()
{
   double pointValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   if(lastSignal == "BUY")
   {
      double askPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      return askPrice + (TakeProfitPoints * pointValue);
   }
   else if(lastSignal == "SELL")
   {
      double bidPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      return bidPrice - (TakeProfitPoints * pointValue);
   }

   return 0;
}

//+------------------------------------------------------------------+
//| Execute buy order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder(double lotSize, double stopLoss, double takeProfit)
{
   double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);

   if(trade.Buy(lotSize, Symbol(), price, stopLoss, takeProfit, TradeComment))
   {
      Print("BUY order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
      lastTradeTime = TimeCurrent();
   }
   else
   {
      Print("ERROR: Failed to execute BUY order. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Execute sell order                                               |
//+------------------------------------------------------------------+
//| Update daily profit tracking                                     |
//+------------------------------------------------------------------+
void UpdateDailyProfit()
{
   static datetime lastUpdateDay = 0;
   datetime currentDay = (datetime)(TimeCurrent() / 86400) * 86400; // Start of current day

   if(currentDay != lastUpdateDay)
   {
      // New day - reset daily profit
      dailyProfit = 0.0;
      lastUpdateDay = currentDay;
      Print("Daily profit reset for new day");
   }

   // Calculate current daily profit
   double currentProfit = 0.0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i) && position.Symbol() == Symbol())
      {
         currentProfit += position.Profit();
      }
   }

   dailyProfit = currentProfit;
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   if(!UseTimeFilter)
      return true;

   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);

   // Check trading hours
   if(timeStruct.hour < StartHour || timeStruct.hour >= EndHour)
      return false;

   return true;
}

//+------------------------------------------------------------------+
//| Display ML trading information on chart                         |
//+------------------------------------------------------------------+
void DisplayInfo()
{
   string info = "";
   info += "=== S.G Neural ML EA ===\n";
   info += "ML Model Status: " + (modelLoaded ? "ACTIVE" : "OFFLINE") + "\n";
   info += "Last ML Signal: " + lastSignal + "\n";
   info += "ML Prediction: " + DoubleToString(lastPrediction, 4) + "\n";
   info += "ML Confidence: " + DoubleToString(lastConfidence, 2) + "\n";
   info += "Daily P&L: " + DoubleToString(dailyProfit, 2) + " USD\n";
   info += "Active Positions: " + IntegerToString(PositionsTotal()) + "/" + IntegerToString(MaxPositions) + "\n";
   info += "Account Balance: " + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2) + " USD\n";
   info += "Last Prediction: " + TimeToString(lastPredictionTime, TIME_MINUTES) + "\n";

   // Current market info
   info += "Current Bid: " + DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_BID), 5) + "\n";
   info += "Current Ask: " + DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_ASK), 5) + "\n";

   Comment(info);
}
//+------------------------------------------------------------------+
void ExecuteSellOrder(double lotSize, double stopLoss, double takeProfit)
{
   double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

   if(trade.Sell(lotSize, Symbol(), price, stopLoss, takeProfit, TradeComment))
   {
      Print("SELL order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
      lastTradeTime = TimeCurrent();
   }
   else
   {
      Print("ERROR: Failed to execute SELL order. Error: ", GetLastError());
   }
}