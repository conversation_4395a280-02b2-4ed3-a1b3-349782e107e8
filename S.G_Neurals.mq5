//+------------------------------------------------------------------+
//|                                                   S.G_neurals.mq5 |
//|                                    STABILITY GWIDIBIRA Neural EA |
//|                                 https://www.mql5.com/en/users/sg |
//+------------------------------------------------------------------+
#property copyright "STABILITY GWIDIBIRA"
#property link      "https://www.mql5.com/en/users/sg"
#property version   "1.00"
#property description "Neural Network Enhanced XAUUSD Trading EA"
#property description "Uses machine learning models for price prediction"
#property description "Implements adaptive risk management and recovery mechanics"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>
#include <Indicators\Indicators.mqh>

//--- Trading objects
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;

//--- Input parameters
input group "=== NEURAL NETWORK SETTINGS ==="
input string   PythonPath = "python";                    // Python executable path
input string   ScriptPath = "predict_realtime.py";       // Prediction script path
input double   SignalThreshold = 0.5;                    // Prediction threshold for signals
input double   ConfidenceThreshold = 1.5;                // Minimum confidence for trading
input int      PredictionInterval = 10;                  // Prediction interval in seconds

input group "=== RISK MANAGEMENT ==="
input double   RiskPercent = 1.0;                        // Risk per trade (% of account)
input double   MaxRiskPercent = 5.0;                     // Maximum total risk (% of account)
input double   StopLossATRMultiplier = 2.0;              // Stop loss ATR multiplier
input double   TakeProfitATRMultiplier = 3.0;            // Take profit ATR multiplier
input double   MinStopLoss = 10.0;                       // Minimum stop loss in points
input double   MaxStopLoss = 100.0;                      // Maximum stop loss in points

input group "=== TRADING SETTINGS ==="
input bool     EnableLongTrades = true;                  // Enable long trades
input bool     EnableShortTrades = true;                 // Enable short trades
input int      MaxPositions = 1;                         // Maximum concurrent positions
input int      MagicNumber = 123456;                     // Magic number for trades
input string   TradeComment = "SG_Neural";               // Trade comment

input group "=== TIME FILTERS ==="
input bool     UseTimeFilter = true;                     // Enable time filter
input int      StartHour = 1;                            // Trading start hour (server time)
input int      EndHour = 23;                             // Trading end hour (server time)
input bool     AvoidNews = true;                         // Avoid trading during news
input int      NewsAvoidMinutes = 30;                    // Minutes to avoid before/after news

input group "=== TECHNICAL INDICATORS ==="
input int      EMA_Period = 21;                          // EMA period for trend filter
input int      RSI_Period = 14;                          // RSI period
input int      ATR_Period = 14;                          // ATR period
input int      MACD_Fast = 12;                           // MACD fast EMA
input int      MACD_Slow = 26;                           // MACD slow EMA
input int      MACD_Signal = 9;                          // MACD signal line

//--- Global variables
datetime       lastPredictionTime = 0;
double         lastPrediction = 0.0;
string         lastSignal = "HOLD";
double         lastConfidence = 0.0;
bool           modelLoaded = false;
int            consecutiveLosses = 0;
double         dailyProfit = 0.0;
datetime       lastTradeTime = 0;

//--- Indicator handles
int            emaHandle;
int            rsiHandle;
int            atrHandle;
int            macdHandle;

//--- Arrays for indicator values
double         emaBuffer[];
double         rsiBuffer[];
double         atrBuffer[];
double         macdMainBuffer[];
double         macdSignalBuffer[];

//--- File handles for Python communication
string         dataFileName = "mt5_data.csv";
string         predictionFileName = "prediction_result.txt";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== S.G Neural EA Initialization ===");
   
   // Set trade parameters
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(Symbol());
   
   // Initialize indicator handles
   if(!InitializeIndicators())
   {
      Print("ERROR: Failed to initialize indicators");
      return INIT_FAILED;
   }
   
   // Test Python integration
   if(!TestPythonIntegration())
   {
      Print("WARNING: Python integration test failed - EA will run with limited functionality");
      modelLoaded = false;
   }
   else
   {
      Print("SUCCESS: Python integration working");
      modelLoaded = true;
   }
   
   // Initialize arrays
   ArraySetAsSeries(emaBuffer, true);
   ArraySetAsSeries(rsiBuffer, true);
   ArraySetAsSeries(atrBuffer, true);
   ArraySetAsSeries(macdMainBuffer, true);
   ArraySetAsSeries(macdSignalBuffer, true);
   
   Print("=== S.G Neural EA Initialized Successfully ===");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("=== S.G Neural EA Deinitialization ===");
   
   // Release indicator handles
   if(emaHandle != INVALID_HANDLE) IndicatorRelease(emaHandle);
   if(rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
   if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
   if(macdHandle != INVALID_HANDLE) IndicatorRelease(macdHandle);
   
   // Clean up temporary files
   FileDelete(dataFileName);
   FileDelete(predictionFileName);
   
   Print("EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if new bar
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(Symbol(), PERIOD_M10, 0);
   bool isNewBar = (currentBarTime != lastBarTime);
   lastBarTime = currentBarTime;
   
   // Update daily profit tracking
   UpdateDailyProfit();
   
   // Check time filters
   if(!IsTimeToTrade())
      return;
   
   // Update technical indicators
   if(!UpdateIndicators())
      return;
   
   // Get neural network prediction (every new bar or based on interval)
   if(isNewBar || (TimeCurrent() - lastPredictionTime) >= PredictionInterval)
   {
      if(modelLoaded)
      {
         GetNeuralPrediction();
      }
      else
      {
         // Fallback to technical analysis only
         GetTechnicalSignal();
      }
   }
   
   // Execute trading logic
   ExecuteTradingLogic();
   
   // Update trailing stops
   UpdateTrailingStops();

   // Display information on chart
   DisplayInfo();
}

//+------------------------------------------------------------------+
//| Initialize technical indicators                                  |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
   // EMA indicator
   emaHandle = iMA(Symbol(), PERIOD_M10, EMA_Period, 0, MODE_EMA, PRICE_CLOSE);
   if(emaHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create EMA indicator");
      return false;
   }

   // RSI indicator
   rsiHandle = iRSI(Symbol(), PERIOD_M10, RSI_Period, PRICE_CLOSE);
   if(rsiHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create RSI indicator");
      return false;
   }

   // ATR indicator
   atrHandle = iATR(Symbol(), PERIOD_M10, ATR_Period);
   if(atrHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create ATR indicator");
      return false;
   }

   // MACD indicator
   macdHandle = iMACD(Symbol(), PERIOD_M10, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
   if(macdHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create MACD indicator");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Update indicator values                                          |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
   // Copy EMA values
   if(CopyBuffer(emaHandle, 0, 0, 3, emaBuffer) < 3)
   {
      Print("ERROR: Failed to copy EMA buffer");
      return false;
   }

   // Copy RSI values
   if(CopyBuffer(rsiHandle, 0, 0, 3, rsiBuffer) < 3)
   {
      Print("ERROR: Failed to copy RSI buffer");
      return false;
   }

   // Copy ATR values
   if(CopyBuffer(atrHandle, 0, 0, 3, atrBuffer) < 3)
   {
      Print("ERROR: Failed to copy ATR buffer");
      return false;
   }

   // Copy MACD values
   if(CopyBuffer(macdHandle, 0, 0, 3, macdMainBuffer) < 3)
   {
      Print("ERROR: Failed to copy MACD main buffer");
      return false;
   }

   if(CopyBuffer(macdHandle, 1, 0, 3, macdSignalBuffer) < 3)
   {
      Print("ERROR: Failed to copy MACD signal buffer");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Test Python integration                                          |
//+------------------------------------------------------------------+
bool TestPythonIntegration()
{
   // Create test data file
   if(!CreateDataFile(50)) // Create file with 50 bars for testing
   {
      Print("ERROR: Failed to create test data file");
      return false;
   }

   // Execute Python script
   string command = PythonPath + " " + ScriptPath;
   int result = WinExec(command, 0);

   if(result <= 31)
   {
      Print("ERROR: Python script execution failed. Return code: ", result);
      return false;
   }

   // Check if prediction file was created
   if(!FileIsExist(predictionFileName))
   {
      Print("ERROR: Prediction file not created");
      return false;
   }

   // Try to read prediction
   string predictionResult = ReadPredictionFile();
   if(predictionResult == "")
   {
      Print("ERROR: Failed to read prediction result");
      return false;
   }

   Print("Python integration test successful. Result: ", predictionResult);
   return true;
}

//+------------------------------------------------------------------+
//| Create data file for Python script                              |
//+------------------------------------------------------------------+
bool CreateDataFile(int bars = 100)
{
   int fileHandle = FileOpen(dataFileName, FILE_WRITE | FILE_CSV | FILE_ANSI);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create data file");
      return false;
   }

   // Write CSV header
   FileWrite(fileHandle, "datetime,open,high,low,close,tick_volume,volume,spread");

   // Write OHLC data
   for(int i = bars - 1; i >= 0; i--)
   {
      datetime time = iTime(Symbol(), PERIOD_M10, i);
      double open = iOpen(Symbol(), PERIOD_M10, i);
      double high = iHigh(Symbol(), PERIOD_M10, i);
      double low = iLow(Symbol(), PERIOD_M10, i);
      double close = iClose(Symbol(), PERIOD_M10, i);
      long tickVolume = iTickVolume(Symbol(), PERIOD_M10, i);
      long volume = iRealVolume(Symbol(), PERIOD_M10, i);
      int spread = (int)SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);

      string timeStr = TimeToString(time, TIME_DATE | TIME_MINUTES);
      FileWrite(fileHandle, timeStr, open, high, low, close, tickVolume, volume, spread);
   }

   FileClose(fileHandle);
   return true;
}

//+------------------------------------------------------------------+
//| Get neural network prediction                                    |
//+------------------------------------------------------------------+
void GetNeuralPrediction()
{
   // Create fresh data file with latest bars
   if(!CreateDataFile(100))
   {
      Print("ERROR: Failed to create data file for prediction");
      return;
   }

   // Execute Python prediction script
   string command = PythonPath + " " + ScriptPath;
   int result = system(command);

   if(result != 0)
   {
      Print("ERROR: Python prediction failed. Return code: ", result);
      return;
   }

   // Read prediction result
   string predictionResult = ReadPredictionFile();
   if(predictionResult != "")
   {
      ParsePredictionResult(predictionResult);
      lastPredictionTime = TimeCurrent();
      Print("Neural prediction: ", lastPrediction, " Signal: ", lastSignal, " Confidence: ", lastConfidence);
   }
}

//+------------------------------------------------------------------+
//| Read prediction result from file                                 |
//+------------------------------------------------------------------+
string ReadPredictionFile()
{
   int fileHandle = FileOpen(predictionFileName, FILE_READ | FILE_TXT | FILE_ANSI);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to open prediction file");
      return "";
   }

   string result = "";
   if(!FileIsEnding(fileHandle))
   {
      result = FileReadString(fileHandle);
   }

   FileClose(fileHandle);
   return result;
}

//+------------------------------------------------------------------+
//| Parse prediction result string                                   |
//+------------------------------------------------------------------+
void ParsePredictionResult(string result)
{
   // Expected format: "SIGNAL:BUY,PREDICTION:0.5432,CONFIDENCE:1.8"
   string parts[];
   int count = StringSplit(result, ',', parts);

   for(int i = 0; i < count; i++)
   {
      string keyValue[];
      if(StringSplit(parts[i], ':', keyValue) == 2)
      {
         if(keyValue[0] == "SIGNAL")
         {
            lastSignal = keyValue[1];
         }
         else if(keyValue[0] == "PREDICTION")
         {
            lastPrediction = StringToDouble(keyValue[1]);
         }
         else if(keyValue[0] == "CONFIDENCE")
         {
            lastConfidence = StringToDouble(keyValue[1]);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Get technical analysis signal (fallback)                        |
//+------------------------------------------------------------------+
void GetTechnicalSignal()
{
   double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double ema = emaBuffer[0];
   double rsi = rsiBuffer[0];
   double atr = atrBuffer[0];
   double macdMain = macdMainBuffer[0];
   double macdSignal = macdSignalBuffer[0];

   // Simple technical analysis logic
   lastConfidence = 1.0; // Base confidence for technical signals

   // Trend analysis
   bool bullishTrend = (currentPrice > ema) && (macdMain > macdSignal);
   bool bearishTrend = (currentPrice < ema) && (macdMain < macdSignal);

   // RSI conditions
   bool rsiOversold = rsi < 30;
   bool rsiOverbought = rsi > 70;
   bool rsiNeutral = (rsi >= 40 && rsi <= 60);

   // Generate signal
   if(bullishTrend && !rsiOverbought && rsiNeutral)
   {
      lastSignal = "BUY";
      lastPrediction = atr * 0.5; // Predict positive movement
      lastConfidence = 1.2;
   }
   else if(bearishTrend && !rsiOversold && rsiNeutral)
   {
      lastSignal = "SELL";
      lastPrediction = -atr * 0.5; // Predict negative movement
      lastConfidence = 1.2;
   }
   else
   {
      lastSignal = "HOLD";
      lastPrediction = 0.0;
      lastConfidence = 0.5;
   }

   lastPredictionTime = TimeCurrent();
   Print("Technical signal: ", lastSignal, " Prediction: ", lastPrediction, " Confidence: ", lastConfidence);
}

//+------------------------------------------------------------------+
//| Execute trading logic                                            |
//+------------------------------------------------------------------+
void ExecuteTradingLogic()
{
   // Check if we have a valid signal
   if(lastSignal == "HOLD" || lastConfidence < ConfidenceThreshold)
      return;

   // Check position limits
   if(PositionsTotal() >= MaxPositions)
      return;

   // Check if we already have a position
   if(position.Select(Symbol()))
   {
      // We have a position, check for exit conditions
      CheckExitConditions();
      return;
   }

   // Calculate position size
   double lotSize = CalculateLotSize();
   if(lotSize <= 0)
      return;

   // Calculate stop loss and take profit
   double stopLoss = CalculateStopLoss();
   double takeProfit = CalculateTakeProfit();

   // Execute trade based on signal
   if(lastSignal == "BUY" && EnableLongTrades)
   {
      ExecuteBuyOrder(lotSize, stopLoss, takeProfit);
   }
   else if(lastSignal == "SELL" && EnableShortTrades)
   {
      ExecuteSellOrder(lotSize, stopLoss, takeProfit);
   }
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk                           |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * RiskPercent / 100.0;

   double stopLossPoints = CalculateStopLossPoints();
   if(stopLossPoints <= 0)
      return 0;

   double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
   double lotSize = riskAmount / (stopLossPoints * tickValue);

   // Apply lot size limits
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(lotSize, minLot);
   lotSize = MathMin(lotSize, maxLot);
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   return lotSize;
}

//+------------------------------------------------------------------+
//| Calculate stop loss in points                                   |
//+------------------------------------------------------------------+
double CalculateStopLossPoints()
{
   double atr = atrBuffer[0];
   double stopLossPoints = atr * StopLossATRMultiplier * MathPow(10, Digits());

   stopLossPoints = MathMax(stopLossPoints, MinStopLoss);
   stopLossPoints = MathMin(stopLossPoints, MaxStopLoss);

   return stopLossPoints;
}

//+------------------------------------------------------------------+
//| Calculate stop loss price                                        |
//+------------------------------------------------------------------+
double CalculateStopLoss()
{
   double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double stopLossPoints = CalculateStopLossPoints();
   double pointValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   if(lastSignal == "BUY")
   {
      return currentPrice - (stopLossPoints * pointValue);
   }
   else if(lastSignal == "SELL")
   {
      return currentPrice + (stopLossPoints * pointValue);
   }

   return 0;
}

//+------------------------------------------------------------------+
//| Calculate take profit price                                      |
//+------------------------------------------------------------------+
double CalculateTakeProfit()
{
   double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double atr = atrBuffer[0];
   double takeProfitPoints = atr * TakeProfitATRMultiplier * MathPow(10, Digits());
   double pointValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   if(lastSignal == "BUY")
   {
      return currentPrice + (takeProfitPoints * pointValue);
   }
   else if(lastSignal == "SELL")
   {
      return currentPrice - (takeProfitPoints * pointValue);
   }

   return 0;
}

//+------------------------------------------------------------------+
//| Execute buy order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder(double lotSize, double stopLoss, double takeProfit)
{
   double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);

   if(trade.Buy(lotSize, Symbol(), price, stopLoss, takeProfit, TradeComment))
   {
      Print("BUY order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
      lastTradeTime = TimeCurrent();
   }
   else
   {
      Print("ERROR: Failed to execute BUY order. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Execute sell order                                               |
//+------------------------------------------------------------------+
void ExecuteSellOrder(double lotSize, double stopLoss, double takeProfit)
{
   double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

   if(trade.Sell(lotSize, Symbol(), price, stopLoss, takeProfit, TradeComment))
   {
      Print("SELL order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
      lastTradeTime = TimeCurrent();
   }
   else
   {
      Print("ERROR: Failed to execute SELL order. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Check exit conditions for existing positions                    |
//+------------------------------------------------------------------+
void CheckExitConditions()
{
   if(!position.Select(Symbol()))
      return;

   // Check for opposite signal
   if((position.PositionType() == POSITION_TYPE_BUY && lastSignal == "SELL") ||
      (position.PositionType() == POSITION_TYPE_SELL && lastSignal == "BUY"))
   {
      if(lastConfidence > ConfidenceThreshold)
      {
         trade.PositionClose(Symbol());
         Print("Position closed due to opposite signal");
      }
   }
}

//+------------------------------------------------------------------+
//| Update trailing stops                                            |
//+------------------------------------------------------------------+
void UpdateTrailingStops()
{
   if(!position.Select(Symbol()))
      return;

   double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double atr = atrBuffer[0];
   double trailDistance = atr * StopLossATRMultiplier;
   double pointValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

   if(position.PositionType() == POSITION_TYPE_BUY)
   {
      double newStopLoss = currentPrice - (trailDistance * pointValue);
      if(newStopLoss > position.StopLoss() && newStopLoss < currentPrice)
      {
         trade.PositionModify(Symbol(), newStopLoss, position.TakeProfit());
         Print("Trailing stop updated for BUY position: ", newStopLoss);
      }
   }
   else if(position.PositionType() == POSITION_TYPE_SELL)
   {
      double newStopLoss = currentPrice + (trailDistance * pointValue);
      if(newStopLoss < position.StopLoss() && newStopLoss > currentPrice)
      {
         trade.PositionModify(Symbol(), newStopLoss, position.TakeProfit());
         Print("Trailing stop updated for SELL position: ", newStopLoss);
      }
   }
}

//+------------------------------------------------------------------+
//| Update daily profit tracking                                     |
//+------------------------------------------------------------------+
void UpdateDailyProfit()
{
   static datetime lastUpdateDay = 0;
   datetime currentDay = (datetime)(TimeCurrent() / 86400) * 86400; // Start of current day

   if(currentDay != lastUpdateDay)
   {
      // New day - reset daily profit
      dailyProfit = 0.0;
      lastUpdateDay = currentDay;
      Print("Daily profit reset for new day");
   }

   // Calculate current daily profit
   double currentProfit = 0.0;
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(position.SelectByIndex(i) && position.Symbol() == Symbol())
      {
         currentProfit += position.Profit();
      }
   }

   dailyProfit = currentProfit;
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   if(!UseTimeFilter)
      return true;

   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);

   // Check trading hours
   if(timeStruct.hour < StartHour || timeStruct.hour >= EndHour)
      return false;

   // Additional time filters can be added here
   // For example, avoid trading during weekends, holidays, etc.

   return true;
}

//+------------------------------------------------------------------+
//| Display information on chart                                     |
//+------------------------------------------------------------------+
void DisplayInfo()
{
   string info = "";
   info += "=== S.G Neural EA ===\n";
   info += "Model Status: " + (modelLoaded ? "LOADED" : "TECHNICAL ONLY") + "\n";
   info += "Last Signal: " + lastSignal + "\n";
   info += "Prediction: " + DoubleToString(lastPrediction, 4) + "\n";
   info += "Confidence: " + DoubleToString(lastConfidence, 2) + "\n";
   info += "Daily P&L: " + DoubleToString(dailyProfit, 2) + "\n";
   info += "Positions: " + IntegerToString(PositionsTotal()) + "/" + IntegerToString(MaxPositions) + "\n";

   // Display current indicator values
   if(ArraySize(emaBuffer) > 0)
   {
      info += "EMA(21): " + DoubleToString(emaBuffer[0], 5) + "\n";
      info += "RSI(14): " + DoubleToString(rsiBuffer[0], 2) + "\n";
      info += "ATR(14): " + DoubleToString(atrBuffer[0], 5) + "\n";
   }

   Comment(info);
}