
//+------------------------------------------------------------------+
//|                                                     SmartSG.mq5 |
//|                                    STABILITY GWIDIBIRA Smart EA |
//|                                 https://www.mql5.com/en/users/sg |
//+------------------------------------------------------------------+
#property copyright "STABILITY GWIDIBIRA"
#property link      "https://www.mql5.com/en/users/sg"
#property version   "1.00"
#property description "SmartEdge XAU/USD Expert Advisor"
#property description "Technical Indicator Based Trading System"
#property description "EMA Crossover + RSI + ATR Strategy"

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\OrderInfo.mqh>

//--- Trading objects
CTrade         trade;
CPositionInfo  position;
COrderInfo     order;

//--- Enum declarations
enum ENUM_RISK_LEVEL
{
   RISK_LOW = 0,      // Low Risk (0.5% per trade)
   RISK_MEDIUM = 1,   // Medium Risk (1.0% per trade)
   RISK_HIGH = 2      // High Risk (2.0% per trade)
};

//--- Input parameters
input group "=== STRATEGY SETTINGS ==="
input int      EMA_Fast = 20;                            // Fast EMA period
input int      EMA_Slow = 50;                            // Slow EMA period
input int      RSI_Period = 14;                          // RSI period
input double   RSI_Buy_Level = 55.0;                     // RSI buy threshold
input double   RSI_Sell_Level = 45.0;                    // RSI sell threshold
input int      ATR_Period = 14;                          // ATR period
input double   ATR_Multiplier = 2.0;                     // ATR multiplier for SL/TP

input group "=== RISK MANAGEMENT ==="
input ENUM_RISK_LEVEL RiskLevel = RISK_MEDIUM;           // Risk level selection

input double   MaxSpread = 30.0;                         // Maximum spread in points
input double   MaxDrawdownPercent = 15.0;                // Maximum drawdown percentage
input double   MinLotSize = 0.01;                        // Minimum lot size
input double   MaxLotSize = 1.0;                         // Maximum lot size

input group "=== TRADING SETTINGS ==="
input bool     EnableLongTrades = true;                  // Enable long trades
input bool     EnableShortTrades = true;                 // Enable short trades
input int      MaxPositions = 1;                         // Maximum concurrent positions
input int      MagicNumber = 789123;                     // Magic number for trades
input string   TradeComment = "SmartSG";                 // Trade comment

input group "=== TIME FILTERS ==="
input bool     UseTimeFilter = true;                     // Enable time filter
input int      StartHour = 2;                            // Trading start hour (server time)
input int      EndHour = 23;                             // Trading end hour (server time)
input bool     TradeMonday = true;                       // Trade on Monday
input bool     TradeTuesday = true;                      // Trade on Tuesday
input bool     TradeWednesday = true;                    // Trade on Wednesday
input bool     TradeThursday = true;                     // Trade on Thursday
input bool     TradeFriday = true;                       // Trade on Friday

input group "=== RECOVERY SETTINGS ==="
input bool     EnableRecovery = true;                    // Enable recovery mode
input int      RecoveryCooldown = 60;                    // Recovery cooldown in minutes
input double   RecoveryLotReduction = 0.5;               // Lot reduction factor after losses
input int      MaxConsecutiveLosses = 3;                 // Max consecutive losses before cooldown

//--- Global variables
int            emaFastHandle;
int            emaSlowHandle;
int            rsiHandle;
int            atrHandle;

double         emaFastBuffer[];
double         emaSlowBuffer[];
double         rsiBuffer[];
double         atrBuffer[];

datetime       lastTradeTime = 0;
datetime       lastSignalTime = 0;
int            consecutiveLosses = 0;
datetime       recoveryEndTime = 0;
double         initialBalance = 0;
bool           recoveryMode = false;

string         lastSignal = "NONE";
double         currentSpread = 0;
bool           indicatorsReady = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== SmartSG EA Initialization ===");
   
   // Set trade parameters
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(Symbol());
   
   // Initialize indicator handles
   if(!InitializeIndicators())
   {
      Print("ERROR: Failed to initialize indicators");
      return INIT_FAILED;
   }
   
   // Set initial balance for drawdown calculation
   initialBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   
   // Initialize arrays
   ArraySetAsSeries(emaFastBuffer, true);
   ArraySetAsSeries(emaSlowBuffer, true);
   ArraySetAsSeries(rsiBuffer, true);
   ArraySetAsSeries(atrBuffer, true);
   
   Print("=== SmartSG EA Initialized Successfully ===");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("=== SmartSG EA Deinitialization ===");
   
   // Release indicator handles
   if(emaFastHandle != INVALID_HANDLE) IndicatorRelease(emaFastHandle);
   if(emaSlowHandle != INVALID_HANDLE) IndicatorRelease(emaSlowHandle);
   if(rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
   if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
   
   Print("EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Update indicator data
   if(!UpdateIndicators())
      return;
   
   // Check trading conditions
   if(!IsTimeToTrade())
      return;
   
   // Check spread filter
   if(!CheckSpreadFilter())
      return;
   
   // Check drawdown protection
   if(!CheckDrawdownProtection())
      return;
   
   // Check recovery mode
   if(!CheckRecoveryMode())
      return;
   
   // Get trading signal
   string signal = GetTradingSignal();
   
   // Execute trading logic
   if(signal != "NONE" && signal != lastSignal)
   {
      ExecuteTradingLogic(signal);
      lastSignal = signal;
      lastSignalTime = TimeCurrent();
   }
   
   // Display information on chart
   DisplayInfo();
}

//+------------------------------------------------------------------+
//| Initialize indicator handles                                     |
//+------------------------------------------------------------------+
bool InitializeIndicators()
{
   // Create EMA handles
   emaFastHandle = iMA(Symbol(), PERIOD_M10, EMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
   emaSlowHandle = iMA(Symbol(), PERIOD_M10, EMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
   
   // Create RSI handle
   rsiHandle = iRSI(Symbol(), PERIOD_M10, RSI_Period, PRICE_CLOSE);
   
   // Create ATR handle
   atrHandle = iATR(Symbol(), PERIOD_M10, ATR_Period);
   
   // Check if all handles are valid
   if(emaFastHandle == INVALID_HANDLE || emaSlowHandle == INVALID_HANDLE ||
      rsiHandle == INVALID_HANDLE || atrHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to create indicator handles");
      return false;
   }
   
   Print("SUCCESS: All indicators initialized");
   return true;
}

//+------------------------------------------------------------------+
//| Update indicator buffers                                         |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
   // Copy indicator data
   if(CopyBuffer(emaFastHandle, 0, 0, 3, emaFastBuffer) < 3 ||
      CopyBuffer(emaSlowHandle, 0, 0, 3, emaSlowBuffer) < 3 ||
      CopyBuffer(rsiHandle, 0, 0, 3, rsiBuffer) < 3 ||
      CopyBuffer(atrHandle, 0, 0, 3, atrBuffer) < 3)
   {
      Print("WARNING: Failed to copy indicator data");
      indicatorsReady = false;
      return false;
   }
   
   indicatorsReady = true;
   return true;
}

//+------------------------------------------------------------------+
//| Get trading signal based on strategy                            |
//+------------------------------------------------------------------+
string GetTradingSignal()
{
   if(!indicatorsReady)
      return "NONE";
   
   // Get current and previous values
   double emaFastCurrent = emaFastBuffer[0];
   double emaFastPrevious = emaFastBuffer[1];
   double emaSlowCurrent = emaSlowBuffer[0];
   double emaSlowPrevious = emaSlowBuffer[1];
   double rsiCurrent = rsiBuffer[0];
   double atrCurrent = atrBuffer[0];
   
   // Check for EMA crossover and RSI confirmation
   bool bullishCrossover = (emaFastPrevious <= emaSlowPrevious) && (emaFastCurrent > emaSlowCurrent);
   bool bearishCrossover = (emaFastPrevious >= emaSlowPrevious) && (emaFastCurrent < emaSlowCurrent);
   
   // ATR volatility filter (minimum volatility required)
   double minVolatility = SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 10; // 10 points minimum
   if(atrCurrent < minVolatility)
      return "NONE";
   
   // Generate signals
   if(bullishCrossover && rsiCurrent > RSI_Buy_Level && EnableLongTrades)
   {
      Print("BULLISH SIGNAL: EMA Crossover + RSI = ", rsiCurrent);
      return "BUY";
   }
   
   if(bearishCrossover && rsiCurrent < RSI_Sell_Level && EnableShortTrades)
   {
      Print("BEARISH SIGNAL: EMA Crossover + RSI = ", rsiCurrent);
      return "SELL";
   }
   
   return "NONE";
}

//+------------------------------------------------------------------+
//| Execute trading logic based on signal                           |
//+------------------------------------------------------------------+
void ExecuteTradingLogic(string signal)
{
   // Check position limits
   if(PositionsTotal() >= MaxPositions)
   {
      Print("Maximum positions reached: ", PositionsTotal());
      return;
   }

   // Check if we already have a position for this symbol
   if(position.Select(Symbol()))
   {
      Print("Position already exists for ", Symbol());
      return;
   }

   // Calculate position size
   double lotSize = CalculateLotSize();
   if(lotSize <= 0)
   {
      Print("Invalid lot size calculated: ", lotSize);
      return;
   }

   // Calculate stop loss and take profit levels
   double stopLoss = CalculateStopLoss(signal);
   double takeProfit = CalculateTakeProfit(signal);

   // Execute trade based on signal
   if(signal == "BUY")
   {
      ExecuteBuyOrder(lotSize, stopLoss, takeProfit);
   }
   else if(signal == "SELL")
   {
      ExecuteSellOrder(lotSize, stopLoss, takeProfit);
   }
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk level                     |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskPercent = 0.0;

   // Set risk percentage based on risk level
   switch(RiskLevel)
   {
      case RISK_LOW:    riskPercent = 0.5; break;
      case RISK_MEDIUM: riskPercent = 1.0; break;
      case RISK_HIGH:   riskPercent = 2.0; break;
   }

   // Apply recovery lot reduction if in recovery mode
   if(recoveryMode && consecutiveLosses > 0)
   {
      riskPercent *= RecoveryLotReduction;
      Print("Recovery mode: Reduced risk to ", riskPercent, "%");
   }

   double riskAmount = accountBalance * riskPercent / 100.0;

   // Calculate lot size based on ATR and stop loss
   double atrValue = atrBuffer[0];
   double stopLossDistance = atrValue * ATR_Multiplier;
   double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
   double lotSize = riskAmount / (stopLossDistance * tickValue / SymbolInfoDouble(Symbol(), SYMBOL_POINT));

   // Apply lot size limits
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(lotSize, MinLotSize);
   lotSize = MathMin(lotSize, MaxLotSize);
   lotSize = MathMin(lotSize, maxLot);
   lotSize = MathMax(lotSize, minLot);

   // Normalize to lot step
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   Print("Calculated lot size: ", lotSize, " Risk: ", riskPercent, "% ATR: ", atrValue);
   return lotSize;
}

//+------------------------------------------------------------------+
//| Calculate stop loss price                                        |
//+------------------------------------------------------------------+
double CalculateStopLoss(string signal)
{
   double atrValue = atrBuffer[0];
   double stopDistance = atrValue * ATR_Multiplier;

   if(signal == "BUY")
   {
      double askPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      return askPrice - stopDistance;
   }
   else if(signal == "SELL")
   {
      double bidPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      return bidPrice + stopDistance;
   }

   return 0;
}

//+------------------------------------------------------------------+
//| Calculate take profit price                                      |
//+------------------------------------------------------------------+
double CalculateTakeProfit(string signal)
{
   double atrValue = atrBuffer[0];
   double profitDistance = atrValue * ATR_Multiplier * 1.5; // 1.5x ATR for TP

   if(signal == "BUY")
   {
      double askPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      return askPrice + profitDistance;
   }
   else if(signal == "SELL")
   {
      double bidPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      return bidPrice - profitDistance;
   }

   return 0;
}

//+------------------------------------------------------------------+
//| Execute buy order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder(double lotSize, double stopLoss, double takeProfit)
{
   double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);

   if(trade.Buy(lotSize, Symbol(), price, stopLoss, takeProfit, TradeComment))
   {
      Print("BUY order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
      lastTradeTime = TimeCurrent();
   }
   else
   {
      Print("ERROR: Failed to execute BUY order. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Execute sell order                                               |
//+------------------------------------------------------------------+
void ExecuteSellOrder(double lotSize, double stopLoss, double takeProfit)
{
   double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

   if(trade.Sell(lotSize, Symbol(), price, stopLoss, takeProfit, TradeComment))
   {
      Print("SELL order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
      lastTradeTime = TimeCurrent();
   }
   else
   {
      Print("ERROR: Failed to execute SELL order. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Check if it's time to trade                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   if(!UseTimeFilter)
      return true;

   datetime currentTime = TimeCurrent();
   MqlDateTime timeStruct;
   TimeToStruct(currentTime, timeStruct);

   // Check trading hours
   if(timeStruct.hour < StartHour || timeStruct.hour >= EndHour)
      return false;

   // Check trading days
   switch(timeStruct.day_of_week)
   {
      case 1: return TradeMonday;    // Monday
      case 2: return TradeTuesday;   // Tuesday
      case 3: return TradeWednesday; // Wednesday
      case 4: return TradeThursday;  // Thursday
      case 5: return TradeFriday;    // Friday
      default: return false;         // Weekend
   }
}

//+------------------------------------------------------------------+
//| Check spread filter                                              |
//+------------------------------------------------------------------+
bool CheckSpreadFilter()
{
   currentSpread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD);

   if(currentSpread > MaxSpread)
   {
      Print("Spread too high: ", currentSpread, " > ", MaxSpread);
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Check drawdown protection                                        |
//+------------------------------------------------------------------+
bool CheckDrawdownProtection()
{
   double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double drawdownPercent = (initialBalance - currentBalance) / initialBalance * 100.0;

   if(drawdownPercent > MaxDrawdownPercent)
   {
      Print("DRAWDOWN PROTECTION: Current drawdown ", drawdownPercent, "% exceeds maximum ", MaxDrawdownPercent, "%");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Check recovery mode status                                       |
//+------------------------------------------------------------------+
bool CheckRecoveryMode()
{
   if(!EnableRecovery)
      return true;

   // Check if recovery cooldown period has ended
   if(recoveryMode && TimeCurrent() >= recoveryEndTime)
   {
      recoveryMode = false;
      consecutiveLosses = 0;
      Print("Recovery mode ended - resuming normal trading");
   }

   // If still in recovery mode, don't trade
   if(recoveryMode)
   {
      Print("Recovery mode active until: ", TimeToString(recoveryEndTime));
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Handle trade result for recovery logic                          |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
   if(!EnableRecovery)
      return;

   // Check if it's a position close
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
   {
      // Get deal info
      if(HistoryDealSelect(trans.deal))
      {
         double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);

         if(profit < 0) // Loss
         {
            consecutiveLosses++;
            Print("Consecutive losses: ", consecutiveLosses);

            // Activate recovery mode if max losses reached
            if(consecutiveLosses >= MaxConsecutiveLosses)
            {
               recoveryMode = true;
               recoveryEndTime = TimeCurrent() + RecoveryCooldown * 60;
               Print("Recovery mode activated for ", RecoveryCooldown, " minutes");
            }
         }
         else if(profit > 0) // Profit
         {
            consecutiveLosses = 0; // Reset counter on profit
            Print("Profitable trade - consecutive losses reset");
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Display trading information on chart                            |
//+------------------------------------------------------------------+
void DisplayInfo()
{
   string info = "";
   info += "=== SmartSG Technical EA ===\n";
   info += "Strategy: EMA(" + IntegerToString(EMA_Fast) + "/" + IntegerToString(EMA_Slow) + ") + RSI(" + IntegerToString(RSI_Period) + ") + ATR(" + IntegerToString(ATR_Period) + ")\n";
   info += "Risk Level: " + EnumToString(RiskLevel) + "\n";
   info += "Last Signal: " + lastSignal + "\n";

   // Current indicator values
   if(indicatorsReady)
   {
      info += "EMA Fast: " + DoubleToString(emaFastBuffer[0], 5) + "\n";
      info += "EMA Slow: " + DoubleToString(emaSlowBuffer[0], 5) + "\n";
      info += "RSI: " + DoubleToString(rsiBuffer[0], 2) + "\n";
      info += "ATR: " + DoubleToString(atrBuffer[0], 5) + "\n";
   }

   // Trading status
   info += "Positions: " + IntegerToString(PositionsTotal()) + "/" + IntegerToString(MaxPositions) + "\n";
   info += "Current Spread: " + DoubleToString(currentSpread, 1) + " points\n";

   // Account info
   double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double drawdown = (initialBalance - currentBalance) / initialBalance * 100.0;
   info += "Account Balance: " + DoubleToString(currentBalance, 2) + " USD\n";
   info += "Drawdown: " + DoubleToString(drawdown, 2) + "%\n";

   // Recovery status
   if(EnableRecovery)
   {
      info += "Recovery Mode: " + (recoveryMode ? "ACTIVE" : "INACTIVE") + "\n";
      info += "Consecutive Losses: " + IntegerToString(consecutiveLosses) + "/" + IntegerToString(MaxConsecutiveLosses) + "\n";
      if(recoveryMode)
      {
         info += "Recovery Ends: " + TimeToString(recoveryEndTime, TIME_MINUTES) + "\n";
      }
   }

   // Current market info
   info += "Current Bid: " + DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_BID), 5) + "\n";
   info += "Current Ask: " + DoubleToString(SymbolInfoDouble(Symbol(), SYMBOL_ASK), 5) + "\n";

   Comment(info);
}