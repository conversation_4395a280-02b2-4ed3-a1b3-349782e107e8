#!/usr/bin/env python3
"""
Test script to verify the neural network model implementation
This script tests the key components without running the full training
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

def test_data_loading():
    """Test data loading and preprocessing"""
    print("Testing data loading...")
    try:
        # Load data
        df = pd.read_csv('gold_data.csv', sep='\t')
        print(f"✓ Data loaded successfully: {df.shape}")
        
        # Check columns
        expected_cols = ['<DATE>', '<TIME>', '<OPEN>', '<HIGH>', '<LOW>', '<CLOSE>', '<TICKVOL>', '<VOL>', '<SPREAD>']
        if all(col in df.columns for col in expected_cols):
            print("✓ All expected columns present")
        else:
            print("✗ Missing columns")
            return False
            
        # Check data types
        if len(df) > 1000:
            print(f"✓ Sufficient data: {len(df)} rows")
        else:
            print("✗ Insufficient data")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Data loading failed: {e}")
        return False

def test_technical_indicators():
    """Test technical indicator calculations"""
    print("\nTesting technical indicators...")
    try:
        # Create sample data
        np.random.seed(42)
        prices = pd.Series(1800 + np.cumsum(np.random.randn(100) * 0.1))
        
        # Test EMA
        ema = prices.ewm(span=21, adjust=False).mean()
        if len(ema) == len(prices) and not ema.isna().all():
            print("✓ EMA calculation works")
        else:
            print("✗ EMA calculation failed")
            return False
            
        # Test RSI
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        if len(rsi) == len(prices) and 0 <= rsi.dropna().max() <= 100:
            print("✓ RSI calculation works")
        else:
            print("✗ RSI calculation failed")
            return False
            
        return True
    except Exception as e:
        print(f"✗ Technical indicators failed: {e}")
        return False

def test_feature_engineering():
    """Test feature engineering pipeline"""
    print("\nTesting feature engineering...")
    try:
        # Create sample OHLCV data
        np.random.seed(42)
        n_samples = 500
        base_price = 1800
        
        data = pd.DataFrame({
            'open': base_price + np.cumsum(np.random.randn(n_samples) * 0.1),
            'high': base_price + np.cumsum(np.random.randn(n_samples) * 0.1) + np.abs(np.random.randn(n_samples) * 0.5),
            'low': base_price + np.cumsum(np.random.randn(n_samples) * 0.1) - np.abs(np.random.randn(n_samples) * 0.5),
            'close': base_price + np.cumsum(np.random.randn(n_samples) * 0.1),
            'tick_volume': np.random.randint(100, 1000, n_samples),
            'volume': np.random.randint(0, 100, n_samples),
            'spread': np.random.randint(1, 20, n_samples)
        })
        
        # Add datetime index
        data.index = pd.date_range('2023-01-01', periods=n_samples, freq='10min')
        
        # Test basic features
        data['returns'] = data['close'].pct_change()
        data['ema_21'] = data['close'].ewm(span=21, adjust=False).mean()
        data['candle_size'] = data['high'] - data['low']
        
        if len(data.dropna()) > 400:  # Should have most data after feature creation
            print("✓ Feature engineering works")
            print(f"  - Created {data.shape[1]} features")
            print(f"  - {len(data.dropna())} valid samples after feature creation")
            return True
        else:
            print("✗ Feature engineering failed - too much data lost")
            return False
            
    except Exception as e:
        print(f"✗ Feature engineering failed: {e}")
        return False

def test_model_architecture():
    """Test neural network architecture"""
    print("\nTesting model architecture...")
    try:
        import tensorflow as tf
        from tensorflow.keras.models import Sequential
        from tensorflow.keras.layers import Dense, Conv1D, MaxPooling1D, Flatten, Dropout, BatchNormalization
        from tensorflow.keras.optimizers import Adam
        
        # Create a simple model
        input_shape = (20, 30)  # 20 time steps, 30 features
        
        model = Sequential([
            Conv1D(filters=32, kernel_size=3, activation='relu', input_shape=input_shape),
            BatchNormalization(),
            MaxPooling1D(pool_size=2),
            Dropout(0.2),
            Flatten(),
            Dense(64, activation='relu'),
            Dropout(0.3),
            Dense(1, activation='linear')
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
        
        print("✓ Model architecture created successfully")
        print(f"  - Input shape: {input_shape}")
        print(f"  - Total parameters: {model.count_params():,}")
        
        # Test with dummy data
        X_dummy = np.random.randn(10, 20, 30)
        y_dummy = np.random.randn(10, 1)
        
        # Test prediction
        pred = model.predict(X_dummy, verbose=0)
        if pred.shape == (10, 1):
            print("✓ Model prediction works")
            return True
        else:
            print("✗ Model prediction failed")
            return False
            
    except Exception as e:
        print(f"✗ Model architecture failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing XAUUSD Neural Network Model Components")
    print("=" * 50)
    
    tests = [
        test_data_loading,
        test_technical_indicators,
        test_feature_engineering,
        test_model_architecture
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"✓ Passed: {sum(results)}/{len(results)} tests")
    
    if all(results):
        print("🎉 All tests passed! The model implementation is ready.")
        print("\n📝 Next steps:")
        print("1. Open and run the 'modell_no_talib.ipynb' notebook")
        print("2. Execute all cells to train the model")
        print("3. Review the performance metrics")
        print("4. Save the trained model for MT5 integration")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return all(results)

if __name__ == "__main__":
    main()
