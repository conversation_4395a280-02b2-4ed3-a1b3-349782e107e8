#!/usr/bin/env python3
"""
Real-time prediction function for XAUUSD trading
This script loads the trained model and provides predictions for new data
"""

import pandas as pd
import numpy as np
import joblib
import warnings
from datetime import datetime, timedelta

warnings.filterwarnings('ignore')

class XAUUSDPredictor:
    """Real-time XAUUSD price prediction class"""
    
    def __init__(self, model_path='models'):
        """Initialize the predictor with trained model"""
        self.model_path = model_path
        self.model = None
        self.scaler = None
        self.feature_columns = None
        self.metadata = None
        self.load_model()
    
    def load_model(self):
        """Load the trained model and preprocessing objects"""
        try:
            self.model = joblib.load(f'{self.model_path}/best_model.pkl')
            self.scaler = joblib.load(f'{self.model_path}/scaler.pkl')
            self.feature_columns = joblib.load(f'{self.model_path}/feature_columns.pkl')
            self.metadata = joblib.load(f'{self.model_path}/metadata.pkl')
            print(f"✅ Model loaded successfully: {self.metadata['model_type']}")
            print(f"   Features: {self.metadata['n_features']}")
            print(f"   Prediction horizon: {self.metadata['prediction_horizon']} periods")
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            raise
    
    def create_features_from_ohlc(self, ohlc_data):
        """
        Create technical features from OHLC data
        
        Args:
            ohlc_data: DataFrame with columns ['open', 'high', 'low', 'close', 'tick_volume', 'volume', 'spread']
                      and datetime index
        
        Returns:
            DataFrame with technical features
        """
        df = ohlc_data.copy()
        
        # Basic price features
        df['hl_ratio'] = df['high'] / df['low']
        df['oc_ratio'] = df['open'] / df['close']
        df['candle_size'] = df['high'] - df['low']
        df['body_size'] = abs(df['close'] - df['open'])
        df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
        df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
        
        # Returns
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Moving averages
        for period in [5, 10, 21, 50]:
            df[f'sma_{period}'] = df['close'].rolling(period).mean()
            df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
            df[f'price_vs_sma_{period}'] = df['close'] / df[f'sma_{period}'] - 1
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # ATR
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        df['atr'] = true_range.rolling(window=14).mean()
        df['atr_ratio'] = df['atr'] / df['atr'].rolling(20).mean()
        
        # MACD
        ema_12 = df['close'].ewm(span=12).mean()
        ema_26 = df['close'].ewm(span=26).mean()
        df['macd'] = ema_12 - ema_26
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_hist'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        for period in [20]:
            sma = df['close'].rolling(period).mean()
            std = df['close'].rolling(period).std()
            df[f'bb_upper_{period}'] = sma + (std * 2)
            df[f'bb_lower_{period}'] = sma - (std * 2)
            df[f'bb_width_{period}'] = (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}']) / sma
            df[f'bb_position_{period}'] = (df['close'] - df[f'bb_lower_{period}']) / (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}'])
        
        # Volatility
        for period in [10, 20, 50]:
            df[f'volatility_{period}'] = df['returns'].rolling(period).std()
        
        # Momentum
        for period in [5, 10, 20]:
            df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
        
        # Volume features
        df['volume_sma'] = df['tick_volume'].rolling(20).mean()
        df['volume_ratio'] = df['tick_volume'] / df['volume_sma']
        
        # Time features
        df['hour'] = df.index.hour
        df['day_of_week'] = df.index.dayofweek
        df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
        df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
        df['dow_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['dow_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        
        return df
    
    def predict_price_movement(self, ohlc_data, threshold=0.5):
        """
        Predict price movement for the latest data point
        
        Args:
            ohlc_data: DataFrame with OHLC data (minimum 50 rows for technical indicators)
            threshold: Threshold for trading signals
        
        Returns:
            Dictionary with prediction and trading signal
        """
        if len(ohlc_data) < 50:
            raise ValueError("Need at least 50 data points for technical indicators")
        
        # Create features
        df_with_features = self.create_features_from_ohlc(ohlc_data)
        
        # Get the latest row with all features
        latest_data = df_with_features.iloc[-1:][self.feature_columns]
        
        # Check for missing values
        if latest_data.isna().any().any():
            print("⚠️  Warning: Some features have missing values, using forward fill")
            latest_data = latest_data.fillna(method='ffill')
        
        # Scale features
        features_scaled = self.scaler.transform(latest_data)
        
        # Make prediction
        prediction = self.model.predict(features_scaled)[0]
        
        # Generate trading signal
        if prediction > threshold:
            signal = "BUY"
            confidence = min(prediction / threshold, 3.0)  # Cap confidence at 3x
        elif prediction < -threshold:
            signal = "SELL"
            confidence = min(abs(prediction) / threshold, 3.0)
        else:
            signal = "HOLD"
            confidence = 0.0
        
        # Get current price and technical indicators for context
        current_price = ohlc_data['close'].iloc[-1]
        ema_21 = df_with_features['ema_21'].iloc[-1]
        rsi = df_with_features['rsi'].iloc[-1]
        atr = df_with_features['atr'].iloc[-1]
        
        return {
            'timestamp': ohlc_data.index[-1],
            'current_price': current_price,
            'prediction': prediction,
            'signal': signal,
            'confidence': confidence,
            'technical_context': {
                'ema_21': ema_21,
                'price_vs_ema21': (current_price / ema_21 - 1) * 100,
                'rsi': rsi,
                'atr': atr
            }
        }

def demo_prediction():
    """Demo function showing how to use the predictor"""
    print("🔮 XAUUSD Real-time Prediction Demo")
    print("=" * 40)
    
    # Load the predictor
    predictor = XAUUSDPredictor()
    
    # Load some sample data (last 100 rows for demo)
    df = pd.read_csv('gold_data.csv', sep='\t')
    df['datetime'] = pd.to_datetime(df['<DATE>'] + ' ' + df['<TIME>'])
    df = df.rename(columns={
        '<OPEN>': 'open', '<HIGH>': 'high', '<LOW>': 'low', '<CLOSE>': 'close',
        '<TICKVOL>': 'tick_volume', '<VOL>': 'volume', '<SPREAD>': 'spread'
    })
    df.set_index('datetime', inplace=True)
    df = df[['open', 'high', 'low', 'close', 'tick_volume', 'volume', 'spread']]
    
    # Use last 100 rows for prediction
    sample_data = df.tail(100)
    
    # Make prediction
    result = predictor.predict_price_movement(sample_data, threshold=0.5)
    
    print(f"📅 Timestamp: {result['timestamp']}")
    print(f"💰 Current Price: {result['current_price']:.2f}")
    print(f"📈 Prediction: {result['prediction']:.4f} points")
    print(f"🎯 Signal: {result['signal']}")
    print(f"🔥 Confidence: {result['confidence']:.2f}")
    print(f"\n📊 Technical Context:")
    print(f"   EMA(21): {result['technical_context']['ema_21']:.2f}")
    print(f"   Price vs EMA21: {result['technical_context']['price_vs_ema21']:.2f}%")
    print(f"   RSI: {result['technical_context']['rsi']:.2f}")
    print(f"   ATR: {result['technical_context']['atr']:.4f}")
    
    # Trading recommendation
    print(f"\n💡 Trading Recommendation:")
    if result['signal'] == 'BUY':
        print(f"   Consider LONG position if price > EMA(21) and RSI < 70")
    elif result['signal'] == 'SELL':
        print(f"   Consider SHORT position if price < EMA(21) and RSI > 30")
    else:
        print(f"   Hold current position or wait for clearer signal")

def main_mt5():
    """Main function for MT5 EA integration"""
    import sys
    import os

    # Check if data file exists (created by MT5 EA)
    data_file = "mt5_data.csv"
    if not os.path.exists(data_file):
        print("ERROR: MT5 data file not found")
        sys.exit(1)

    try:
        # Load data from MT5
        data = pd.read_csv(data_file, index_col=0, parse_dates=True)

        # Make prediction
        predictor = XAUUSDPredictor()
        result = predictor.predict_price_movement(data)

        # Write result to file for MT5 EA to read
        output_file = "prediction_result.txt"
        with open(output_file, 'w') as f:
            f.write(f"SIGNAL:{result['signal']},PREDICTION:{result['prediction']:.4f},CONFIDENCE:{result['confidence']:.2f}")

        print(f"Prediction completed: {result['signal']} ({result['prediction']:.4f})")

    except Exception as e:
        print(f"ERROR: {str(e)}")
        # Write error to file
        with open("prediction_result.txt", 'w') as f:
            f.write("SIGNAL:HOLD,PREDICTION:0.0000,CONFIDENCE:0.0")
        sys.exit(1)

if __name__ == "__main__":
    # Check if running in MT5 mode (data file exists) or demo mode
    if os.path.exists("mt5_data.csv"):
        main_mt5()
    else:
        demo_prediction()
