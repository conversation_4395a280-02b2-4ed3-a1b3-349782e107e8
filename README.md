Here’s an enhanced **MVP-style breakdown** of the **SmartChoise EA**—everything you need to grasp its design and prepare your own version:

---

## 🧠 Project: **SmartChoise — Neural‑Driven Gold Trading EA (M1/XAUUSD)**

### 🎯 Objective

Build an **MT5 Expert Advisor** that trades Gold (XAUUSD) on short timeframes using **neural network logic**, **adaptive risk**, and **recovery mechanics**, without grid or martingale systems. Designed for **steady growth** and **controlled risk**.

---

### 🧩 Core Features (what it does)

* **Neural Network Decision Engine**
  Continuously analyzes real-time and historical market data to decide when to enter/exit trades ([mql5.com][1], [eafxstore.com][2]).

* **Adaptive Lot Sizing**
  Dynamically sizes each trade based on:

  * Account equity
  * Volatility
  * Market conditions
  * Chosen risk level (Low/Medium/High/Extreme) ([mql5.com][1])

* **Multiple Risk Modes**
  Choose from pre-set configurations:

  * Risk levels (Low, Medium, High, Extreme)
  * Trading style (conservative vs aggressive) ([mql5.com][1])

* **Hard Stop Limit**
  Stop trading when daily drawdown exceeds a set percentage (e.g., 20%) ([mql5.com][1]).

* **Recovery & Exit Strategy**
  Includes a recovery mechanism to recoup losses intelligently (not martingale). Optionally uses virtual SL/TP to keep stops hidden from brokers—for better exit execution ([eafxstore.com][3]).

* **Broker/Latency Adaptation**
  Tunes its trade entry/exit parameters based on spread, slippage, and execution speed ([arxiv.org][4], [eafxstore.com][5]).

* **Strategy Extensions (Opt-In)**
  Optionally enable support/resistance, candlestick patterns, or other filters for added entry signals ([mql5.com][1]).

---

### ⚙️ Trading Logic Flow

1. **Market Scan** — Every tick on M1 (or optionally M5/M15/M30).
2. **NN Prediction** — Neural model assesses if conditions match its trained patterns.
3. **Check Conditions** — Spread, drawdown, trading hours, news filter.
4. **Calculate Lot Size** — Based on risk profile and volatility metrics.
5. **Place Order** — Enter buy/sell with dynamic or virtual SL/TP.
6. **Recovery Handling** — On losses, adjust exposure; hidden/proxy SL maintains safety.
7. **Close Trades** — When profit targets are met or hard-stop reached.
8. **Track Performance** daily to respect drawdown caps.

---

### 🎛️ Configuration Inputs (Inputs you'll need)

```mql5
input ENUM_RiskLevel risk = RISK_MEDIUM;
input bool useRecovery = true;
input double hardStopPercent = 20;
input double maxSpread = 2.0; // in points
input ENUM_Timeframes timeframe = PERIOD_M1;
input bool enableCandlesFilter = false;
```

---

### 🛠 Technical Building Blocks

* **Neural Network Training**

  * Use Python + frameworks (TensorFlow/Keras or PyTorch)
  * Train model on historical XAUUSD data, then export weights
  * Encode forward-pass in MQL5 (or integrate C++ DLL)

* **Indicators & Conditions**

  * Compute volatility (ATR)
  * Monitor spread/slippage using `SymbolInfoDouble`
  * Implement news/time filters

* **Risk & Recovery Engine**

  * Calculate lot size via equity and ATR
  * Track drawdown as `Equity / Highest_Equity`
  * Use `OrderSend()` and hidden SL/TP methods

* **Trade Management**

  * Monitor open positions
  * Adjust exits or close trades when conditions met

* **User Panel**

  * Show metrics: current risk, DD%, total trades/profit

---

### ⚠️ Key Challenges

* **ML Integration**
  Deploying neural network models efficiently within MQL5 requires either embedded inference or DLL integration.

* **Risk Sensitivity**
  Avoid overfitting—ensure robust parameterization across market regimes.

* **Broker-Specific Tuning**
  Backtest and test with your target broker to account for spread/slippage differences.

* **Hidden SL/TP Implementation**
  Using virtual stop placements needs careful simulation to avoid execution risk.

* **Control & Monitoring**
  Implement safeguards for max-positions, max-trades-per-day, and session timing.

---

### 🚀 Why This Is Powerful

* **AI Integration** — Goes well beyond simple indicators—explicitly uses neural networks for entry logic.
* **Risk-Managed Automation** — Shows command of real-world challenges: spread, drawdown, recovery.
* **Modularity** — You can extend with support/resistance, candlestick filters, or multi-asset logic.
* **Market Ready** — Built as a robust piece—suitable for demo/live testing and portfolio inclusion.

---

### 🏁 Next Steps

1. **Design your NN pipeline** in Python, train on XAUUSD M1 historical data.
2. **Prototype entries** based on simple indicators first; embed SL/TP logic.
3. **Implement recovery engine**, lot sizing, and hard stop limits.
4. **Wrap it all in MQL5**, integrate or DLL your model inference.
5. **Backtest extensively**, parameter-sweep through different market conditions.
6. **Add trade info UI**, logging, and validation routines.

---

Let me know if you want help building your **training pipeline**, embedding the neural model in MQL5, or creating modular code so you can swap strategies easily.

[1]: https://www.mql5.com/en/market/product/128606?utm_source=chatgpt.com "SmartChoise | Buy Trading Robot (Expert Advisor) for MetaTrader 5"
[2]: https://eafxstore.com/product/smartchoise-mt4/?srsltid=AfmBOormJ6V2ZlZXYK46dv49LIA31a7hlrv7I_UZSPBU-HzZ417SkyEE&utm_source=chatgpt.com "SmartChoise MT4 (Latest version) - PreOrder - EA FX Store"
[3]: https://eafxstore.com/product/smartchoise-mt4/?srsltid=AfmBOor-lm8MXse5iu-dtFJHcXeWZkr3N-Zsor_TcJiXAxlnE6tXT8lm&utm_source=chatgpt.com "SmartChoise MT4 (Latest version) - PreOrder - EA FX Store"
[4]: https://arxiv.org/abs/1810.00619?utm_source=chatgpt.com "SmartChoices: Hybridizing Programming and Machine Learning"
[5]: https://eafxstore.com/product/smartchoise-mt5/?srsltid=AfmBOopVA7GtFClfrxehd4_L3w6NhOtypwl_7Gtru2IFtHVojRQ3tOqZ&utm_source=chatgpt.com "SmartChoise EA MT5 (Latest version) – GroupBuy - EA FX Store"
