#!/usr/bin/env python3
"""
Simplified XAUUSD Trading Model using Scikit-learn
This version uses traditional ML models instead of deep learning to avoid TensorFlow issues
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import os

warnings.filterwarnings('ignore')

def load_and_preprocess_data():
    """Load and preprocess the gold data"""
    print("Loading data...")
    df = pd.read_csv('gold_data.csv', sep='\t')
    
    # Combine date and time columns
    df['datetime'] = pd.to_datetime(df['<DATE>'] + ' ' + df['<TIME>'])
    
    # Rename columns
    df = df.rename(columns={
        '<OPEN>': 'open',
        '<HIGH>': 'high', 
        '<LOW>': 'low',
        '<CLOSE>': 'close',
        '<TICKVOL>': 'tick_volume',
        '<VOL>': 'volume',
        '<SPREAD>': 'spread'
    })
    
    # Set datetime as index and sort
    df.set_index('datetime', inplace=True)
    df.drop(['<DATE>', '<TIME>'], axis=1, inplace=True)
    df.sort_index(inplace=True)
    
    print(f"Data loaded: {df.shape}")
    return df

def create_technical_features(data):
    """Create technical indicators and features"""
    print("Creating technical features...")
    df = data.copy()
    
    # Basic price features
    df['hl_ratio'] = df['high'] / df['low']
    df['oc_ratio'] = df['open'] / df['close']
    df['candle_size'] = df['high'] - df['low']
    df['body_size'] = abs(df['close'] - df['open'])
    df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
    df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
    
    # Returns
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    
    # Moving averages
    for period in [5, 10, 21, 50]:
        df[f'sma_{period}'] = df['close'].rolling(period).mean()
        df[f'ema_{period}'] = df['close'].ewm(span=period).mean()
        df[f'price_vs_sma_{period}'] = df['close'] / df[f'sma_{period}'] - 1
    
    # RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # ATR
    high_low = df['high'] - df['low']
    high_close = np.abs(df['high'] - df['close'].shift())
    low_close = np.abs(df['low'] - df['close'].shift())
    true_range = np.maximum(high_low, np.maximum(high_close, low_close))
    df['atr'] = true_range.rolling(window=14).mean()
    df['atr_ratio'] = df['atr'] / df['atr'].rolling(20).mean()
    
    # MACD
    ema_12 = df['close'].ewm(span=12).mean()
    ema_26 = df['close'].ewm(span=26).mean()
    df['macd'] = ema_12 - ema_26
    df['macd_signal'] = df['macd'].ewm(span=9).mean()
    df['macd_hist'] = df['macd'] - df['macd_signal']
    
    # Bollinger Bands
    for period in [20]:
        sma = df['close'].rolling(period).mean()
        std = df['close'].rolling(period).std()
        df[f'bb_upper_{period}'] = sma + (std * 2)
        df[f'bb_lower_{period}'] = sma - (std * 2)
        df[f'bb_width_{period}'] = (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}']) / sma
        df[f'bb_position_{period}'] = (df['close'] - df[f'bb_lower_{period}']) / (df[f'bb_upper_{period}'] - df[f'bb_lower_{period}'])
    
    # Volatility
    for period in [10, 20, 50]:
        df[f'volatility_{period}'] = df['returns'].rolling(period).std()
    
    # Momentum
    for period in [5, 10, 20]:
        df[f'momentum_{period}'] = df['close'] / df['close'].shift(period) - 1
    
    # Volume features
    df['volume_sma'] = df['tick_volume'].rolling(20).mean()
    df['volume_ratio'] = df['tick_volume'] / df['volume_sma']
    
    # Time features
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['dow_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['dow_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    
    print(f"Features created: {df.shape[1]} columns")
    return df

def create_target_and_features(data, prediction_horizon=5):
    """Create target variable and prepare features"""
    print("Creating target variable...")
    df = data.copy()
    
    # Create target: price change over next 5 periods
    df['future_close'] = df['close'].shift(-prediction_horizon)
    df['target'] = df['future_close'] - df['close']
    
    # Remove rows where we can't calculate target
    df = df[:-prediction_horizon]
    
    # Select feature columns (exclude OHLC and target columns)
    exclude_cols = ['open', 'high', 'low', 'close', 'tick_volume', 'volume', 'spread', 
                   'future_close', 'target', 'hour', 'day_of_week']
    feature_cols = [col for col in df.columns if col not in exclude_cols]
    
    # Remove any columns with all NaN values
    feature_cols = [col for col in feature_cols if not df[col].isna().all()]
    
    print(f"Selected {len(feature_cols)} features")
    return df, feature_cols

def train_models(X, y, test_size=0.2):
    """Train multiple models and compare performance"""
    print("Training models...")
    
    # Split data (time series split)
    split_idx = int(len(X) * (1 - test_size))
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    print(f"Train samples: {len(X_train)}, Test samples: {len(X_test)}")
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Train models
    models = {
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1),
        'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42)
    }
    
    results = {}
    trained_models = {}
    
    for name, model in models.items():
        print(f"Training {name}...")
        model.fit(X_train_scaled, y_train)
        
        # Predictions
        y_pred_train = model.predict(X_train_scaled)
        y_pred_test = model.predict(X_test_scaled)
        
        # Metrics
        train_mse = mean_squared_error(y_train, y_pred_train)
        test_mse = mean_squared_error(y_test, y_pred_test)
        test_mae = mean_absolute_error(y_test, y_pred_test)
        test_r2 = r2_score(y_test, y_pred_test)
        
        # Directional accuracy
        train_dir_acc = np.mean(np.sign(y_train) == np.sign(y_pred_train))
        test_dir_acc = np.mean(np.sign(y_test) == np.sign(y_pred_test))
        
        results[name] = {
            'train_mse': train_mse,
            'test_mse': test_mse,
            'test_mae': test_mae,
            'test_r2': test_r2,
            'train_dir_acc': train_dir_acc,
            'test_dir_acc': test_dir_acc,
            'predictions': y_pred_test
        }
        
        trained_models[name] = model
        
        print(f"{name} Results:")
        print(f"  Test MSE: {test_mse:.6f}")
        print(f"  Test MAE: {test_mae:.6f}")
        print(f"  Test R²: {test_r2:.6f}")
        print(f"  Test Directional Accuracy: {test_dir_acc:.4f} ({test_dir_acc*100:.2f}%)")
        print()
    
    return results, trained_models, scaler, (X_test_scaled, y_test)

def evaluate_trading_performance(y_true, y_pred, threshold=0.5):
    """Evaluate trading performance"""
    buy_signals = y_pred > threshold
    sell_signals = y_pred < -threshold
    
    # Trading metrics
    buy_accuracy = np.mean(y_true[buy_signals] > 0) if np.sum(buy_signals) > 0 else 0
    sell_accuracy = np.mean(y_true[sell_signals] < 0) if np.sum(sell_signals) > 0 else 0
    
    # Profit simulation
    profits = []
    for i in range(len(y_true)):
        if buy_signals[i]:
            profits.append(y_true[i])  # Long position profit/loss
        elif sell_signals[i]:
            profits.append(-y_true[i])  # Short position profit/loss
    
    total_profit = np.sum(profits) if profits else 0
    avg_profit = np.mean(profits) if profits else 0
    
    return {
        'buy_signals': np.sum(buy_signals),
        'sell_signals': np.sum(sell_signals),
        'buy_accuracy': buy_accuracy,
        'sell_accuracy': sell_accuracy,
        'total_trades': len(profits),
        'total_profit': total_profit,
        'avg_profit_per_trade': avg_profit
    }

def main():
    """Main execution function"""
    print("🚀 XAUUSD Trading Model Training (Scikit-learn Version)")
    print("=" * 60)
    
    # Load and preprocess data
    data = load_and_preprocess_data()
    
    # Create features
    data_with_features = create_technical_features(data)
    
    # Create target and select features
    final_data, feature_cols = create_target_and_features(data_with_features)
    
    # Remove NaN values
    clean_data = final_data.dropna()
    print(f"Clean data shape: {clean_data.shape}")
    
    # Prepare X and y
    X = clean_data[feature_cols].values
    y = clean_data['target'].values
    
    print(f"Feature matrix shape: {X.shape}")
    print(f"Target vector shape: {y.shape}")
    
    # Train models
    results, models, scaler, (X_test, y_test) = train_models(X, y)
    
    # Evaluate trading performance for best model
    best_model_name = min(results.keys(), key=lambda k: results[k]['test_mse'])
    best_predictions = results[best_model_name]['predictions']
    
    print(f"Best model: {best_model_name}")
    trading_metrics = evaluate_trading_performance(y_test, best_predictions)
    
    print("\n📊 Trading Performance:")
    print(f"Buy signals: {trading_metrics['buy_signals']}")
    print(f"Sell signals: {trading_metrics['sell_signals']}")
    print(f"Buy accuracy: {trading_metrics['buy_accuracy']:.4f} ({trading_metrics['buy_accuracy']*100:.2f}%)")
    print(f"Sell accuracy: {trading_metrics['sell_accuracy']:.4f} ({trading_metrics['sell_accuracy']*100:.2f}%)")
    print(f"Total trades: {trading_metrics['total_trades']}")
    print(f"Total profit: {trading_metrics['total_profit']:.4f} points")
    print(f"Average profit per trade: {trading_metrics['avg_profit_per_trade']:.4f} points")
    
    # Save the best model
    os.makedirs('models', exist_ok=True)
    best_model = models[best_model_name]
    
    joblib.dump(best_model, 'models/best_model.pkl')
    joblib.dump(scaler, 'models/scaler.pkl')
    joblib.dump(feature_cols, 'models/feature_columns.pkl')
    
    metadata = {
        'model_type': best_model_name,
        'n_features': len(feature_cols),
        'prediction_horizon': 5,
        'performance_metrics': results[best_model_name],
        'trading_metrics': trading_metrics
    }
    joblib.dump(metadata, 'models/metadata.pkl')
    
    print(f"\n💾 Model saved: models/best_model.pkl")
    print("✅ Training completed successfully!")

if __name__ == "__main__":
    main()
