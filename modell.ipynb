# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# Deep Learning libraries
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import Dense, Conv1D, MaxPooling1D, Flatten, Dropout, BatchNormalization, LSTM
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau

# Technical Analysis
import talib

print(f"TensorFlow version: {tf.__version__}")
print(f"GPU Available: {tf.config.list_physical_devices('GPU')}")

# Load and examine the gold data
df = pd.read_csv('gold_data.csv', sep='\t')

print("Dataset shape:", df.shape)
print("\nColumn names:")
print(df.columns.tolist())
print("\nFirst few rows:")
print(df.head())
print("\nData types:")
print(df.dtypes)
print("\nBasic statistics:")
print(df.describe())

# Data preprocessing and cleaning
def preprocess_data(df):
    """
    Clean and preprocess the raw OHLCV data
    """
    # Create a copy to avoid modifying original data
    data = df.copy()
    
    # Combine date and time columns
    data['datetime'] = pd.to_datetime(data['<DATE>'] + ' ' + data['<TIME>'])
    
    # Rename columns for easier access
    data = data.rename(columns={
        '<OPEN>': 'open',
        '<HIGH>': 'high', 
        '<LOW>': 'low',
        '<CLOSE>': 'close',
        '<TICKVOL>': 'tick_volume',
        '<VOL>': 'volume',
        '<SPREAD>': 'spread'
    })
    
    # Set datetime as index
    data.set_index('datetime', inplace=True)
    
    # Drop original date/time columns
    data.drop(['<DATE>', '<TIME>'], axis=1, inplace=True)
    
    # Sort by datetime
    data.sort_index(inplace=True)
    
    # Check for missing values
    print(f"Missing values: {data.isnull().sum().sum()}")
    
    # Remove any rows with missing values
    data.dropna(inplace=True)
    
    return data

# Preprocess the data
data = preprocess_data(df)
print(f"\nProcessed data shape: {data.shape}")
print(f"Date range: {data.index.min()} to {data.index.max()}")
print("\nProcessed data sample:")
print(data.head())

# Feature Engineering - Create technical indicators and features
def create_features(data):
    """
    Create comprehensive features for the neural network model
    Following the strategy from modelCreation.md
    """
    df = data.copy()
    
    # Basic price features
    df['hl_ratio'] = df['high'] / df['low']
    df['oc_ratio'] = df['open'] / df['close']
    df['candle_size'] = df['high'] - df['low']
    df['body_size'] = abs(df['close'] - df['open'])
    df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
    df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
    
    # Returns and log returns
    df['returns'] = df['close'].pct_change()
    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
    
    # Moving averages (EMAs as specified)
    df['ema_21'] = talib.EMA(df['close'].values, timeperiod=21)
    df['ema_50'] = talib.EMA(df['close'].values, timeperiod=50)
    df['ema_200'] = talib.EMA(df['close'].values, timeperiod=200)
    
    # Price relative to EMAs
    df['price_vs_ema21'] = df['close'] / df['ema_21'] - 1
    df['price_vs_ema50'] = df['close'] / df['ema_50'] - 1
    
    # Technical indicators
    df['rsi'] = talib.RSI(df['close'].values, timeperiod=14)
    df['atr'] = talib.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=14)
    
    # MACD
    macd, macd_signal, macd_hist = talib.MACD(df['close'].values)
    df['macd'] = macd
    df['macd_signal'] = macd_signal
    df['macd_hist'] = macd_hist
    
    # Bollinger Bands
    bb_upper, bb_middle, bb_lower = talib.BBANDS(df['close'].values, timeperiod=20)
    df['bb_upper'] = bb_upper
    df['bb_lower'] = bb_lower
    df['bb_width'] = (bb_upper - bb_lower) / bb_middle
    df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
    
    # Volatility measures
    df['volatility_20'] = df['returns'].rolling(20).std()
    df['atr_ratio'] = df['atr'] / df['atr'].rolling(20).mean()
    
    # Momentum indicators
    df['momentum_5'] = df['close'] / df['close'].shift(5) - 1
    df['momentum_10'] = df['close'] / df['close'].shift(10) - 1
    df['momentum_20'] = df['close'] / df['close'].shift(20) - 1
    
    # Volume indicators
    df['volume_sma'] = df['tick_volume'].rolling(20).mean()
    df['volume_ratio'] = df['tick_volume'] / df['volume_sma']
    
    # Temporal features
    df['hour'] = df.index.hour
    df['day_of_week'] = df.index.dayofweek
    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)
    
    # Cyclical encoding for time features
    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
    df['dow_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
    df['dow_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
    
    return df

# Create features
print("Creating features...")
data_with_features = create_features(data)
print(f"Data shape after feature engineering: {data_with_features.shape}")
print(f"Number of features created: {data_with_features.shape[1]}")

# Create target variable - Price delta prediction
def create_target(data, prediction_horizon=5):
    """
    Create target variable for price prediction
    Following Option 1 from modelCreation.md: Price Delta (Continuous)
    
    Args:
        data: DataFrame with OHLC data
        prediction_horizon: Number of periods ahead to predict (default: 5 = 50 minutes)
    
    Returns:
        DataFrame with target variable added
    """
    df = data.copy()
    
    # Calculate future price (5 periods ahead)
    df['future_close'] = df['close'].shift(-prediction_horizon)
    
    # Calculate price delta (target variable)
    df['target'] = df['future_close'] - df['close']
    
    # Calculate percentage change for analysis
    df['target_pct'] = (df['future_close'] / df['close'] - 1) * 100
    
    # Remove rows where we can't calculate future price
    df = df[:-prediction_horizon]
    
    return df

# Create target variable
print("Creating target variable...")
data_with_target = create_target(data_with_features, prediction_horizon=5)
print(f"Data shape after target creation: {data_with_target.shape}")

# Analyze target distribution
print("\nTarget variable statistics:")
print(data_with_target['target'].describe())
print(f"\nTarget standard deviation: {data_with_target['target'].std():.4f}")
print(f"Target range: [{data_with_target['target'].min():.4f}, {data_with_target['target'].max():.4f}]")

# Plot target distribution
plt.figure(figsize=(12, 4))
plt.subplot(1, 2, 1)
plt.hist(data_with_target['target'], bins=50, alpha=0.7, edgecolor='black')
plt.title('Target Distribution (Price Delta)')
plt.xlabel('Price Delta')
plt.ylabel('Frequency')

plt.subplot(1, 2, 2)
plt.hist(data_with_target['target_pct'], bins=50, alpha=0.7, edgecolor='black')
plt.title('Target Distribution (Percentage Change)')
plt.xlabel('Percentage Change (%)')
plt.ylabel('Frequency')
plt.tight_layout()
plt.show()

# Prepare data for neural network training
def prepare_data_for_training(data, window_size=20, test_size=0.15, val_size=0.15):
    """
    Prepare windowed data for neural network training
    
    Args:
        data: DataFrame with features and target
        window_size: Number of time steps to look back (default: 20)
        test_size: Proportion of data for testing
        val_size: Proportion of data for validation
    
    Returns:
        Tuple of (X_train, X_val, X_test, y_train, y_val, y_test, feature_names, scaler)
    """
    # Remove rows with NaN values
    df_clean = data.dropna()
    print(f"Clean data shape: {df_clean.shape}")
    
    # Select feature columns (exclude target and auxiliary columns)
    exclude_cols = ['target', 'target_pct', 'future_close', 'open', 'high', 'low', 'close']
    feature_cols = [col for col in df_clean.columns if col not in exclude_cols]
    
    print(f"Number of features selected: {len(feature_cols)}")
    print("Feature columns:", feature_cols[:10], "...")  # Show first 10 features
    
    # Extract features and target
    X = df_clean[feature_cols].values
    y = df_clean['target'].values
    
    # Normalize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Create windowed sequences
    def create_sequences(X, y, window_size):
        X_seq, y_seq = [], []
        for i in range(window_size, len(X)):
            X_seq.append(X_scaled[i-window_size:i])
            y_seq.append(y[i])
        return np.array(X_seq), np.array(y_seq)
    
    X_sequences, y_sequences = create_sequences(X_scaled, y, window_size)
    print(f"Sequence shape: X={X_sequences.shape}, y={y_sequences.shape}")
    
    # Split data: 70% train, 15% val, 15% test
    train_size = 1 - test_size - val_size
    
    # Calculate split indices
    n_samples = len(X_sequences)
    train_end = int(n_samples * train_size)
    val_end = int(n_samples * (train_size + val_size))
    
    # Split sequentially (important for time series)
    X_train = X_sequences[:train_end]
    X_val = X_sequences[train_end:val_end]
    X_test = X_sequences[val_end:]
    
    y_train = y_sequences[:train_end]
    y_val = y_sequences[train_end:val_end]
    y_test = y_sequences[val_end:]
    
    print(f"\nData splits:")
    print(f"Train: X={X_train.shape}, y={y_train.shape}")
    print(f"Val:   X={X_val.shape}, y={y_val.shape}")
    print(f"Test:  X={X_test.shape}, y={y_test.shape}")
    
    return X_train, X_val, X_test, y_train, y_val, y_test, feature_cols, scaler

# Prepare data for training
print("Preparing data for neural network training...")
X_train, X_val, X_test, y_train, y_val, y_test, feature_names, scaler = prepare_data_for_training(
    data_with_target, window_size=20
)

# Neural Network Architecture
def create_neural_network(input_shape, learning_rate=0.001):
    """
    Create neural network model following the architecture from modelCreation.md:
    1D Convolution + Dense layers for sequential pattern detection
    
    Args:
        input_shape: Shape of input data (window_size, n_features)
        learning_rate: Learning rate for optimizer
    
    Returns:
        Compiled Keras model
    """
    model = Sequential([
        # 1D Convolutional layers for pattern detection
        Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=input_shape),
        BatchNormalization(),
        Conv1D(filters=64, kernel_size=3, activation='relu'),
        MaxPooling1D(pool_size=2),
        Dropout(0.2),
        
        Conv1D(filters=32, kernel_size=3, activation='relu'),
        BatchNormalization(),
        MaxPooling1D(pool_size=2),
        Dropout(0.2),
        
        # Flatten for dense layers
        Flatten(),
        
        # Dense layers
        Dense(128, activation='relu'),
        BatchNormalization(),
        Dropout(0.3),
        
        Dense(64, activation='relu'),
        BatchNormalization(),
        Dropout(0.3),
        
        Dense(32, activation='relu'),
        Dropout(0.2),
        
        # Output layer (1 neuron for price delta prediction)
        Dense(1, activation='linear')
    ])
    
    # Compile model
    model.compile(
        optimizer=Adam(learning_rate=learning_rate),
        loss='mse',
        metrics=['mae']
    )
    
    return model

# Create the model
input_shape = (X_train.shape[1], X_train.shape[2])  # (window_size, n_features)
print(f"Input shape: {input_shape}")

model = create_neural_network(input_shape)
print("\nModel architecture:")
model.summary()

# Model Training
def train_model(model, X_train, y_train, X_val, y_val, epochs=100, batch_size=32):
    """
    Train the neural network model with callbacks
    
    Args:
        model: Compiled Keras model
        X_train, y_train: Training data
        X_val, y_val: Validation data
        epochs: Maximum number of epochs
        batch_size: Batch size for training
    
    Returns:
        Training history
    """
    # Define callbacks
    callbacks = [
        EarlyStopping(
            monitor='val_loss',
            patience=15,
            restore_best_weights=True,
            verbose=1
        ),
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=8,
            min_lr=1e-7,
            verbose=1
        )
    ]
    
    # Train the model
    history = model.fit(
        X_train, y_train,
        validation_data=(X_val, y_val),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=callbacks,
        verbose=1
    )
    
    return history

# Train the model
print("Starting model training...")
history = train_model(model, X_train, y_train, X_val, y_val, epochs=100, batch_size=32)
print("Training completed!")

# Visualize training history
def plot_training_history(history):
    """
    Plot training and validation loss/metrics
    """
    fig, axes = plt.subplots(1, 2, figsize=(15, 5))
    
    # Plot loss
    axes[0].plot(history.history['loss'], label='Training Loss', alpha=0.8)
    axes[0].plot(history.history['val_loss'], label='Validation Loss', alpha=0.8)
    axes[0].set_title('Model Loss')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss (MSE)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Plot MAE
    axes[1].plot(history.history['mae'], label='Training MAE', alpha=0.8)
    axes[1].plot(history.history['val_mae'], label='Validation MAE', alpha=0.8)
    axes[1].set_title('Model MAE')
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('Mean Absolute Error')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Plot training history
plot_training_history(history)

# Model Evaluation
def evaluate_model(model, X_test, y_test, threshold=0.5):
    """
    Evaluate model performance on test data
    
    Args:
        model: Trained model
        X_test, y_test: Test data
        threshold: Threshold for trading decisions (in price points)
    
    Returns:
        Dictionary with evaluation metrics
    """
    # Make predictions
    y_pred = model.predict(X_test, verbose=0)
    y_pred = y_pred.flatten()
    
    # Calculate regression metrics
    mse = mean_squared_error(y_test, y_pred)
    mae = mean_absolute_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_test, y_pred)
    
    # Calculate directional accuracy
    actual_direction = np.sign(y_test)
    predicted_direction = np.sign(y_pred)
    directional_accuracy = np.mean(actual_direction == predicted_direction)
    
    # Trading simulation with threshold
    buy_signals = y_pred > threshold
    sell_signals = y_pred < -threshold
    
    # Calculate trading metrics
    buy_accuracy = np.mean(y_test[buy_signals] > 0) if np.sum(buy_signals) > 0 else 0
    sell_accuracy = np.mean(y_test[sell_signals] < 0) if np.sum(sell_signals) > 0 else 0
    
    # Profit simulation (simplified)
    profits = []
    for i in range(len(y_test)):
        if buy_signals[i] and y_test[i] > 0:
            profits.append(y_test[i])
        elif sell_signals[i] and y_test[i] < 0:
            profits.append(-y_test[i])  # Profit from short position
        elif buy_signals[i] and y_test[i] <= 0:
            profits.append(y_test[i])  # Loss from long position
        elif sell_signals[i] and y_test[i] >= 0:
            profits.append(-y_test[i])  # Loss from short position
    
    total_profit = np.sum(profits) if profits else 0
    avg_profit_per_trade = np.mean(profits) if profits else 0
    
    metrics = {
        'mse': mse,
        'mae': mae,
        'rmse': rmse,
        'r2_score': r2,
        'directional_accuracy': directional_accuracy,
        'buy_signals': np.sum(buy_signals),
        'sell_signals': np.sum(sell_signals),
        'buy_accuracy': buy_accuracy,
        'sell_accuracy': sell_accuracy,
        'total_trades': len(profits),
        'total_profit': total_profit,
        'avg_profit_per_trade': avg_profit_per_trade
    }
    
    return metrics, y_pred

# Evaluate the model
print("Evaluating model performance...")
metrics, predictions = evaluate_model(model, X_test, y_test, threshold=0.5)

print("\n=== Model Performance Metrics ===")
print(f"MSE: {metrics['mse']:.6f}")
print(f"MAE: {metrics['mae']:.6f}")
print(f"RMSE: {metrics['rmse']:.6f}")
print(f"R² Score: {metrics['r2_score']:.6f}")
print(f"Directional Accuracy: {metrics['directional_accuracy']:.4f} ({metrics['directional_accuracy']*100:.2f}%)")

print("\n=== Trading Simulation Results ===")
print(f"Buy Signals: {metrics['buy_signals']}")
print(f"Sell Signals: {metrics['sell_signals']}")
print(f"Buy Accuracy: {metrics['buy_accuracy']:.4f} ({metrics['buy_accuracy']*100:.2f}%)")
print(f"Sell Accuracy: {metrics['sell_accuracy']:.4f} ({metrics['sell_accuracy']*100:.2f}%)")
print(f"Total Trades: {metrics['total_trades']}")
print(f"Total Profit: {metrics['total_profit']:.4f} points")
print(f"Average Profit per Trade: {metrics['avg_profit_per_trade']:.4f} points")

# Visualize predictions vs actual values
def plot_predictions(y_test, y_pred, n_samples=500):
    """
    Plot actual vs predicted values
    """
    # Take a subset for visualization
    indices = np.random.choice(len(y_test), min(n_samples, len(y_test)), replace=False)
    y_test_sample = y_test[indices]
    y_pred_sample = y_pred[indices]
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Scatter plot: Predicted vs Actual
    axes[0, 0].scatter(y_test_sample, y_pred_sample, alpha=0.6, s=20)
    axes[0, 0].plot([y_test_sample.min(), y_test_sample.max()], 
                    [y_test_sample.min(), y_test_sample.max()], 'r--', lw=2)
    axes[0, 0].set_xlabel('Actual Values')
    axes[0, 0].set_ylabel('Predicted Values')
    axes[0, 0].set_title('Predicted vs Actual Values')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Time series plot
    sample_indices = np.arange(min(200, len(y_test)))
    axes[0, 1].plot(sample_indices, y_test[:len(sample_indices)], label='Actual', alpha=0.8)
    axes[0, 1].plot(sample_indices, y_pred[:len(sample_indices)], label='Predicted', alpha=0.8)
    axes[0, 1].set_xlabel('Time Steps')
    axes[0, 1].set_ylabel('Price Delta')
    axes[0, 1].set_title('Time Series: Actual vs Predicted')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Residuals plot
    residuals = y_test - y_pred
    axes[1, 0].scatter(y_pred, residuals, alpha=0.6, s=20)
    axes[1, 0].axhline(y=0, color='r', linestyle='--')
    axes[1, 0].set_xlabel('Predicted Values')
    axes[1, 0].set_ylabel('Residuals')
    axes[1, 0].set_title('Residuals Plot')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Residuals histogram
    axes[1, 1].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
    axes[1, 1].set_xlabel('Residuals')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('Residuals Distribution')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Plot predictions
plot_predictions(y_test, predictions)

# Save the trained model and preprocessing objects
import joblib
import os

# Create models directory if it doesn't exist
os.makedirs('models', exist_ok=True)

# Save the trained model
model.save('models/xauusd_neural_model.h5')
print("Model saved to: models/xauusd_neural_model.h5")

# Save the scaler
joblib.dump(scaler, 'models/feature_scaler.pkl')
print("Scaler saved to: models/feature_scaler.pkl")

# Save feature names
joblib.dump(feature_names, 'models/feature_names.pkl')
print("Feature names saved to: models/feature_names.pkl")

# Save model metadata
metadata = {
    'window_size': 20,
    'prediction_horizon': 5,
    'n_features': len(feature_names),
    'input_shape': input_shape,
    'model_metrics': metrics,
    'training_samples': len(X_train),
    'validation_samples': len(X_val),
    'test_samples': len(X_test)
}

joblib.dump(metadata, 'models/model_metadata.pkl')
print("Metadata saved to: models/model_metadata.pkl")

print("\nAll model artifacts saved successfully!")

# Create prediction function for real-time use
def create_prediction_function():
    """
    Create a function that can be used for real-time predictions
    This function can be integrated into an MT5 EA
    """
    def predict_price_movement(recent_data, model, scaler, feature_names, window_size=20):
        """
        Predict price movement for the next 5 periods (50 minutes)
        
        Args:
            recent_data: DataFrame with recent OHLCV data (at least window_size + buffer rows)
            model: Trained neural network model
            scaler: Fitted StandardScaler
            feature_names: List of feature column names
            window_size: Number of time steps to look back
        
        Returns:
            Dictionary with prediction results
        """
        try:
            # Create features for recent data
            data_with_features = create_features(recent_data)
            
            # Select only the required features
            feature_data = data_with_features[feature_names].dropna()
            
            if len(feature_data) < window_size:
                return {
                    'error': f'Insufficient data. Need at least {window_size} rows, got {len(feature_data)}',
                    'prediction': None,
                    'confidence': None
                }
            
            # Take the last window_size rows
            recent_features = feature_data.iloc[-window_size:].values
            
            # Scale features
            recent_features_scaled = scaler.transform(recent_features)
            
            # Reshape for model input
            X_input = recent_features_scaled.reshape(1, window_size, -1)
            
            # Make prediction
            prediction = model.predict(X_input, verbose=0)[0][0]
            
            # Calculate confidence based on recent model performance
            # This is a simplified confidence measure
            abs_prediction = abs(prediction)
            confidence = min(abs_prediction / 2.0, 1.0)  # Normalize to 0-1
            
            # Determine trading signal
            threshold = 0.5  # Can be adjusted based on backtesting
            if prediction > threshold:
                signal = 'BUY'
            elif prediction < -threshold:
                signal = 'SELL'
            else:
                signal = 'HOLD'
            
            return {
                'prediction': float(prediction),
                'signal': signal,
                'confidence': float(confidence),
                'threshold': threshold,
                'timestamp': recent_data.index[-1] if hasattr(recent_data, 'index') else None,
                'error': None
            }
            
        except Exception as e:
            return {
                'error': str(e),
                'prediction': None,
                'signal': 'ERROR',
                'confidence': None
            }
    
    return predict_price_movement

# Create the prediction function
predict_price_movement = create_prediction_function()

# Test the prediction function with recent data
print("Testing prediction function with recent data...")
test_prediction = predict_price_movement(
    data.iloc[-50:],  # Use last 50 rows for testing
    model, 
    scaler, 
    feature_names
)

print("\n=== Test Prediction Result ===")
for key, value in test_prediction.items():
    print(f"{key}: {value}")