{"cells": [{"cell_type": "markdown", "id": "model-overview", "metadata": {}, "source": ["# XAUUSD Neural Network Trading Model\n", "\n", "This notebook implements a neural network-based price prediction model for XAUUSD (Gold) trading.\n", "The model follows the strategy outlined in modelCreation.md:\n", "\n", "- **Goal**: Predict short-term price movements using regression\n", "- **Architecture**: 1D CNN + Dense layers for sequential pattern detection\n", "- **Features**: Technical indicators, price action, volatility measures\n", "- **Target**: <PERSON> delta over next 5 candles (50 minutes)\n"]}, {"cell_type": "code", "execution_count": null, "id": "imports", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Machine Learning libraries\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "\n", "# Deep Learning libraries\n", "import tensorflow as tf\n", "from tensorflow.keras.models import Sequential, Model\n", "from tensorflow.keras.layers import Dense, Conv1D, MaxPooling1D, Flatten, Dropout, BatchNormalization, LSTM\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau\n", "\n", "# Technical Analysis\n", "import talib\n", "\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"GPU Available: {tf.config.list_physical_devices('GPU')}\")"]}, {"cell_type": "code", "execution_count": null, "id": "data-loading", "metadata": {}, "outputs": [], "source": ["# Load and examine the gold data\n", "df = pd.read_csv('gold_data.csv', sep='\\t')\n", "\n", "print(\"Dataset shape:\", df.shape)\n", "print(\"\\nColumn names:\")\n", "print(df.columns.tolist())\n", "print(\"\\nFirst few rows:\")\n", "print(df.head())\n", "print(\"\\nData types:\")\n", "print(df.dtypes)\n", "print(\"\\nBasic statistics:\")\n", "print(df.describe())"]}, {"cell_type": "code", "execution_count": null, "id": "data-preprocessing", "metadata": {}, "outputs": [], "source": ["# Data preprocessing and cleaning\n", "def preprocess_data(df):\n", "    \"\"\"\n", "    Clean and preprocess the raw OHLCV data\n", "    \"\"\"\n", "    # Create a copy to avoid modifying original data\n", "    data = df.copy()\n", "    \n", "    # Combine date and time columns\n", "    data['datetime'] = pd.to_datetime(data['<DATE>'] + ' ' + data['<TIME>'])\n", "    \n", "    # Rename columns for easier access\n", "    data = data.rename(columns={\n", "        '<OPEN>': 'open',\n", "        '<HIGH>': 'high', \n", "        '<LOW>': 'low',\n", "        '<CLOSE>': 'close',\n", "        '<TICKVOL>': 'tick_volume',\n", "        '<VOL>': 'volume',\n", "        '<SPREAD>': 'spread'\n", "    })\n", "    \n", "    # Set datetime as index\n", "    data.set_index('datetime', inplace=True)\n", "    \n", "    # Drop original date/time columns\n", "    data.drop(['<DATE>', '<TIME>'], axis=1, inplace=True)\n", "    \n", "    # Sort by datetime\n", "    data.sort_index(inplace=True)\n", "    \n", "    # Check for missing values\n", "    print(f\"Missing values: {data.isnull().sum().sum()}\")\n", "    \n", "    # Remove any rows with missing values\n", "    data.dropna(inplace=True)\n", "    \n", "    return data\n", "\n", "# Preprocess the data\n", "data = preprocess_data(df)\n", "print(f\"\\nProcessed data shape: {data.shape}\")\n", "print(f\"Date range: {data.index.min()} to {data.index.max()}\")\n", "print(\"\\nProcessed data sample:\")\n", "print(data.head())"]}, {"cell_type": "code", "execution_count": null, "id": "feature-engineering", "metadata": {}, "outputs": [], "source": ["# Feature Engineering - Create technical indicators and features\n", "def create_features(data):\n", "    \"\"\"\n", "    Create comprehensive features for the neural network model\n", "    Following the strategy from modelCreation.md\n", "    \"\"\"\n", "    df = data.copy()\n", "    \n", "    # Basic price features\n", "    df['hl_ratio'] = df['high'] / df['low']\n", "    df['oc_ratio'] = df['open'] / df['close']\n", "    df['candle_size'] = df['high'] - df['low']\n", "    df['body_size'] = abs(df['close'] - df['open'])\n", "    df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])\n", "    df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']\n", "    \n", "    # Returns and log returns\n", "    df['returns'] = df['close'].pct_change()\n", "    df['log_returns'] = np.log(df['close'] / df['close'].shift(1))\n", "    \n", "    # Moving averages (EMAs as specified)\n", "    df['ema_21'] = talib.EMA(df['close'].values, timeperiod=21)\n", "    df['ema_50'] = talib.EMA(df['close'].values, timeperiod=50)\n", "    df['ema_200'] = talib.EMA(df['close'].values, timeperiod=200)\n", "    \n", "    # Price relative to EMAs\n", "    df['price_vs_ema21'] = df['close'] / df['ema_21'] - 1\n", "    df['price_vs_ema50'] = df['close'] / df['ema_50'] - 1\n", "    \n", "    # Technical indicators\n", "    df['rsi'] = talib.RSI(df['close'].values, timeperiod=14)\n", "    df['atr'] = talib.ATR(df['high'].values, df['low'].values, df['close'].values, timeperiod=14)\n", "    \n", "    # MACD\n", "    macd, macd_signal, macd_hist = talib.MACD(df['close'].values)\n", "    df['macd'] = macd\n", "    df['macd_signal'] = macd_signal\n", "    df['macd_hist'] = macd_hist\n", "    \n", "    # Bollinger Bands\n", "    bb_upper, bb_middle, bb_lower = talib.BBANDS(df['close'].values, timeperiod=20)\n", "    df['bb_upper'] = bb_upper\n", "    df['bb_lower'] = bb_lower\n", "    df['bb_width'] = (bb_upper - bb_lower) / bb_middle\n", "    df['bb_position'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)\n", "    \n", "    # Volatility measures\n", "    df['volatility_20'] = df['returns'].rolling(20).std()\n", "    df['atr_ratio'] = df['atr'] / df['atr'].rolling(20).mean()\n", "    \n", "    # Momentum indicators\n", "    df['momentum_5'] = df['close'] / df['close'].shift(5) - 1\n", "    df['momentum_10'] = df['close'] / df['close'].shift(10) - 1\n", "    df['momentum_20'] = df['close'] / df['close'].shift(20) - 1\n", "    \n", "    # Volume indicators\n", "    df['volume_sma'] = df['tick_volume'].rolling(20).mean()\n", "    df['volume_ratio'] = df['tick_volume'] / df['volume_sma']\n", "    \n", "    # Temporal features\n", "    df['hour'] = df.index.hour\n", "    df['day_of_week'] = df.index.dayofweek\n", "    df['is_weekend'] = (df.index.dayofweek >= 5).astype(int)\n", "    \n", "    # Cyclical encoding for time features\n", "    df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)\n", "    df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)\n", "    df['dow_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)\n", "    df['dow_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)\n", "    \n", "    return df\n", "\n", "# Create features\n", "print(\"Creating features...\")\n", "data_with_features = create_features(data)\n", "print(f\"Data shape after feature engineering: {data_with_features.shape}\")\n", "print(f\"Number of features created: {data_with_features.shape[1]}\")"]}, {"cell_type": "code", "execution_count": null, "id": "target-creation", "metadata": {}, "outputs": [], "source": ["# Create target variable - Price delta prediction\n", "def create_target(data, prediction_horizon=5):\n", "    \"\"\"\n", "    Create target variable for price prediction\n", "    Following Option 1 from modelCreation.md: Price Delta (Continuous)\n", "    \n", "    Args:\n", "        data: DataFrame with OHLC data\n", "        prediction_horizon: Number of periods ahead to predict (default: 5 = 50 minutes)\n", "    \n", "    Returns:\n", "        DataFrame with target variable added\n", "    \"\"\"\n", "    df = data.copy()\n", "    \n", "    # Calculate future price (5 periods ahead)\n", "    df['future_close'] = df['close'].shift(-prediction_horizon)\n", "    \n", "    # Calculate price delta (target variable)\n", "    df['target'] = df['future_close'] - df['close']\n", "    \n", "    # Calculate percentage change for analysis\n", "    df['target_pct'] = (df['future_close'] / df['close'] - 1) * 100\n", "    \n", "    # Remove rows where we can't calculate future price\n", "    df = df[:-prediction_horizon]\n", "    \n", "    return df\n", "\n", "# Create target variable\n", "print(\"Creating target variable...\")\n", "data_with_target = create_target(data_with_features, prediction_horizon=5)\n", "print(f\"Data shape after target creation: {data_with_target.shape}\")\n", "\n", "# Analyze target distribution\n", "print(\"\\nTarget variable statistics:\")\n", "print(data_with_target['target'].describe())\n", "print(f\"\\nTarget standard deviation: {data_with_target['target'].std():.4f}\")\n", "print(f\"Target range: [{data_with_target['target'].min():.4f}, {data_with_target['target'].max():.4f}]\")\n", "\n", "# Plot target distribution\n", "plt.figure(figsize=(12, 4))\n", "plt.subplot(1, 2, 1)\n", "plt.hist(data_with_target['target'], bins=50, alpha=0.7, edgecolor='black')\n", "plt.title('Target Distribution (Price Delta)')\n", "plt.xlabel('Price Delta')\n", "plt.ylabel('Frequency')\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.hist(data_with_target['target_pct'], bins=50, alpha=0.7, edgecolor='black')\n", "plt.title('Target Distribution (Percentage Change)')\n", "plt.xlabel('Percentage Change (%)')\n", "plt.ylabel('Frequency')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "data-preparation", "metadata": {}, "outputs": [], "source": ["# Prepare data for neural network training\n", "def prepare_data_for_training(data, window_size=20, test_size=0.15, val_size=0.15):\n", "    \"\"\"\n", "    Prepare windowed data for neural network training\n", "    \n", "    Args:\n", "        data: DataFrame with features and target\n", "        window_size: Number of time steps to look back (default: 20)\n", "        test_size: Proportion of data for testing\n", "        val_size: Proportion of data for validation\n", "    \n", "    Returns:\n", "        Tuple of (X_train, X_val, X_test, y_train, y_val, y_test, feature_names, scaler)\n", "    \"\"\"\n", "    # Remove rows with NaN values\n", "    df_clean = data.dropna()\n", "    print(f\"Clean data shape: {df_clean.shape}\")\n", "    \n", "    # Select feature columns (exclude target and auxiliary columns)\n", "    exclude_cols = ['target', 'target_pct', 'future_close', 'open', 'high', 'low', 'close']\n", "    feature_cols = [col for col in df_clean.columns if col not in exclude_cols]\n", "    \n", "    print(f\"Number of features selected: {len(feature_cols)}\")\n", "    print(\"Feature columns:\", feature_cols[:10], \"...\")  # Show first 10 features\n", "    \n", "    # Extract features and target\n", "    X = df_clean[feature_cols].values\n", "    y = df_clean['target'].values\n", "    \n", "    # Normalize features\n", "    scaler = StandardScaler()\n", "    X_scaled = scaler.fit_transform(X)\n", "    \n", "    # Create windowed sequences\n", "    def create_sequences(X, y, window_size):\n", "        X_seq, y_seq = [], []\n", "        for i in range(window_size, len(X)):\n", "            X_seq.append(X_scaled[i-window_size:i])\n", "            y_seq.append(y[i])\n", "        return np.array(X_seq), np.array(y_seq)\n", "    \n", "    X_sequences, y_sequences = create_sequences(X_scaled, y, window_size)\n", "    print(f\"Sequence shape: X={X_sequences.shape}, y={y_sequences.shape}\")\n", "    \n", "    # Split data: 70% train, 15% val, 15% test\n", "    train_size = 1 - test_size - val_size\n", "    \n", "    # Calculate split indices\n", "    n_samples = len(X_sequences)\n", "    train_end = int(n_samples * train_size)\n", "    val_end = int(n_samples * (train_size + val_size))\n", "    \n", "    # Split sequentially (important for time series)\n", "    X_train = X_sequences[:train_end]\n", "    X_val = X_sequences[train_end:val_end]\n", "    X_test = X_sequences[val_end:]\n", "    \n", "    y_train = y_sequences[:train_end]\n", "    y_val = y_sequences[train_end:val_end]\n", "    y_test = y_sequences[val_end:]\n", "    \n", "    print(f\"\\nData splits:\")\n", "    print(f\"Train: X={X_train.shape}, y={y_train.shape}\")\n", "    print(f\"Val:   X={X_val.shape}, y={y_val.shape}\")\n", "    print(f\"Test:  X={X_test.shape}, y={y_test.shape}\")\n", "    \n", "    return X_train, X_val, X_test, y_train, y_val, y_test, feature_cols, scaler\n", "\n", "# Prepare data for training\n", "print(\"Preparing data for neural network training...\")\n", "X_train, X_val, X_test, y_train, y_val, y_test, feature_names, scaler = prepare_data_for_training(\n", "    data_with_target, window_size=20\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "model-architecture", "metadata": {}, "outputs": [], "source": ["# Neural Network Architecture\n", "def create_neural_network(input_shape, learning_rate=0.001):\n", "    \"\"\"\n", "    Create neural network model following the architecture from modelCreation.md:\n", "    1D Convolution + Dense layers for sequential pattern detection\n", "    \n", "    Args:\n", "        input_shape: Shape of input data (window_size, n_features)\n", "        learning_rate: Learning rate for optimizer\n", "    \n", "    Returns:\n", "        Compiled Keras model\n", "    \"\"\"\n", "    model = Sequential([\n", "        # 1D Convolutional layers for pattern detection\n", "        Conv1D(filters=64, kernel_size=3, activation='relu', input_shape=input_shape),\n", "        BatchNormalization(),\n", "        Conv1D(filters=64, kernel_size=3, activation='relu'),\n", "        MaxPooling1D(pool_size=2),\n", "        Dropout(0.2),\n", "        \n", "        Conv1D(filters=32, kernel_size=3, activation='relu'),\n", "        BatchNormalization(),\n", "        MaxPooling1D(pool_size=2),\n", "        Dropout(0.2),\n", "        \n", "        # Flatten for dense layers\n", "        <PERSON><PERSON>(),\n", "        \n", "        # Dense layers\n", "        Dense(128, activation='relu'),\n", "        BatchNormalization(),\n", "        Dropout(0.3),\n", "        \n", "        <PERSON><PERSON>(64, activation='relu'),\n", "        BatchNormalization(),\n", "        Dropout(0.3),\n", "        \n", "        <PERSON><PERSON>(32, activation='relu'),\n", "        Dropout(0.2),\n", "        \n", "        # Output layer (1 neuron for price delta prediction)\n", "        Dense(1, activation='linear')\n", "    ])\n", "    \n", "    # Compile model\n", "    model.compile(\n", "        optimizer=Adam(learning_rate=learning_rate),\n", "        loss='mse',\n", "        metrics=['mae']\n", "    )\n", "    \n", "    return model\n", "\n", "# Create the model\n", "input_shape = (X_train.shape[1], X_train.shape[2])  # (window_size, n_features)\n", "print(f\"Input shape: {input_shape}\")\n", "\n", "model = create_neural_network(input_shape)\n", "print(\"\\nModel architecture:\")\n", "model.summary()"]}, {"cell_type": "code", "execution_count": null, "id": "model-training", "metadata": {}, "outputs": [], "source": ["# Model Training\n", "def train_model(model, X_train, y_train, X_val, y_val, epochs=100, batch_size=32):\n", "    \"\"\"\n", "    Train the neural network model with callbacks\n", "    \n", "    Args:\n", "        model: Compiled Keras model\n", "        X_train, y_train: Training data\n", "        X_val, y_val: Validation data\n", "        epochs: Maximum number of epochs\n", "        batch_size: Batch size for training\n", "    \n", "    Returns:\n", "        Training history\n", "    \"\"\"\n", "    # Define callbacks\n", "    callbacks = [\n", "        EarlyStopping(\n", "            monitor='val_loss',\n", "            patience=15,\n", "            restore_best_weights=True,\n", "            verbose=1\n", "        ),\n", "        ReduceLROnPlateau(\n", "            monitor='val_loss',\n", "            factor=0.5,\n", "            patience=8,\n", "            min_lr=1e-7,\n", "            verbose=1\n", "        )\n", "    ]\n", "    \n", "    # Train the model\n", "    history = model.fit(\n", "        X_train, y_train,\n", "        validation_data=(X_val, y_val),\n", "        epochs=epochs,\n", "        batch_size=batch_size,\n", "        callbacks=callbacks,\n", "        verbose=1\n", "    )\n", "    \n", "    return history\n", "\n", "# Train the model\n", "print(\"Starting model training...\")\n", "history = train_model(model, X_train, y_train, X_val, y_val, epochs=100, batch_size=32)\n", "print(\"Training completed!\")"]}, {"cell_type": "code", "execution_count": null, "id": "training-visualization", "metadata": {}, "outputs": [], "source": ["# Visualize training history\n", "def plot_training_history(history):\n", "    \"\"\"\n", "    Plot training and validation loss/metrics\n", "    \"\"\"\n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "    # Plot loss\n", "    axes[0].plot(history.history['loss'], label='Training Loss', alpha=0.8)\n", "    axes[0].plot(history.history['val_loss'], label='Validation Loss', alpha=0.8)\n", "    axes[0].set_title('Model Loss')\n", "    axes[0].set_xlabel('Epoch')\n", "    axes[0].set_ylabel('Loss (MSE)')\n", "    axes[0].legend()\n", "    axes[0].grid(True, alpha=0.3)\n", "    \n", "    # Plot MAE\n", "    axes[1].plot(history.history['mae'], label='Training MAE', alpha=0.8)\n", "    axes[1].plot(history.history['val_mae'], label='Validation MAE', alpha=0.8)\n", "    axes[1].set_title('Model MAE')\n", "    axes[1].set_xlabel('Epoch')\n", "    axes[1].set_ylabel('Mean Absolute Error')\n", "    axes[1].legend()\n", "    axes[1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Plot training history\n", "plot_training_history(history)"]}, {"cell_type": "code", "execution_count": null, "id": "model-evaluation", "metadata": {}, "outputs": [], "source": ["# Model Evaluation\n", "def evaluate_model(model, X_test, y_test, threshold=0.5):\n", "    \"\"\"\n", "    Evaluate model performance on test data\n", "    \n", "    Args:\n", "        model: Trained model\n", "        X_test, y_test: Test data\n", "        threshold: Threshold for trading decisions (in price points)\n", "    \n", "    Returns:\n", "        Dictionary with evaluation metrics\n", "    \"\"\"\n", "    # Make predictions\n", "    y_pred = model.predict(X_test, verbose=0)\n", "    y_pred = y_pred.flatten()\n", "    \n", "    # Calculate regression metrics\n", "    mse = mean_squared_error(y_test, y_pred)\n", "    mae = mean_absolute_error(y_test, y_pred)\n", "    rmse = np.sqrt(mse)\n", "    r2 = r2_score(y_test, y_pred)\n", "    \n", "    # Calculate directional accuracy\n", "    actual_direction = np.sign(y_test)\n", "    predicted_direction = np.sign(y_pred)\n", "    directional_accuracy = np.mean(actual_direction == predicted_direction)\n", "    \n", "    # Trading simulation with threshold\n", "    buy_signals = y_pred > threshold\n", "    sell_signals = y_pred < -threshold\n", "    \n", "    # Calculate trading metrics\n", "    buy_accuracy = np.mean(y_test[buy_signals] > 0) if np.sum(buy_signals) > 0 else 0\n", "    sell_accuracy = np.mean(y_test[sell_signals] < 0) if np.sum(sell_signals) > 0 else 0\n", "    \n", "    # Profit simulation (simplified)\n", "    profits = []\n", "    for i in range(len(y_test)):\n", "        if buy_signals[i] and y_test[i] > 0:\n", "            profits.append(y_test[i])\n", "        elif sell_signals[i] and y_test[i] < 0:\n", "            profits.append(-y_test[i])  # Profit from short position\n", "        elif buy_signals[i] and y_test[i] <= 0:\n", "            profits.append(y_test[i])  # Loss from long position\n", "        elif sell_signals[i] and y_test[i] >= 0:\n", "            profits.append(-y_test[i])  # Loss from short position\n", "    \n", "    total_profit = np.sum(profits) if profits else 0\n", "    avg_profit_per_trade = np.mean(profits) if profits else 0\n", "    \n", "    metrics = {\n", "        'mse': mse,\n", "        'mae': mae,\n", "        'rmse': rmse,\n", "        'r2_score': r2,\n", "        'directional_accuracy': directional_accuracy,\n", "        'buy_signals': np.sum(buy_signals),\n", "        'sell_signals': np.sum(sell_signals),\n", "        'buy_accuracy': buy_accuracy,\n", "        'sell_accuracy': sell_accuracy,\n", "        'total_trades': len(profits),\n", "        'total_profit': total_profit,\n", "        'avg_profit_per_trade': avg_profit_per_trade\n", "    }\n", "    \n", "    return metrics, y_pred\n", "\n", "# Evaluate the model\n", "print(\"Evaluating model performance...\")\n", "metrics, predictions = evaluate_model(model, X_test, y_test, threshold=0.5)\n", "\n", "print(\"\\n=== Model Performance Metrics ===\")\n", "print(f\"MSE: {metrics['mse']:.6f}\")\n", "print(f\"MAE: {metrics['mae']:.6f}\")\n", "print(f\"RMSE: {metrics['rmse']:.6f}\")\n", "print(f\"R² Score: {metrics['r2_score']:.6f}\")\n", "print(f\"Directional Accuracy: {metrics['directional_accuracy']:.4f} ({metrics['directional_accuracy']*100:.2f}%)\")\n", "\n", "print(\"\\n=== Trading Simulation Results ===\")\n", "print(f\"Buy Signals: {metrics['buy_signals']}\")\n", "print(f\"Sell Signals: {metrics['sell_signals']}\")\n", "print(f\"Buy Accuracy: {metrics['buy_accuracy']:.4f} ({metrics['buy_accuracy']*100:.2f}%)\")\n", "print(f\"Sell Accuracy: {metrics['sell_accuracy']:.4f} ({metrics['sell_accuracy']*100:.2f}%)\")\n", "print(f\"Total Trades: {metrics['total_trades']}\")\n", "print(f\"Total Profit: {metrics['total_profit']:.4f} points\")\n", "print(f\"Average Profit per Trade: {metrics['avg_profit_per_trade']:.4f} points\")"]}, {"cell_type": "code", "execution_count": null, "id": "prediction-visualization", "metadata": {}, "outputs": [], "source": ["# Visualize predictions vs actual values\n", "def plot_predictions(y_test, y_pred, n_samples=500):\n", "    \"\"\"\n", "    Plot actual vs predicted values\n", "    \"\"\"\n", "    # Take a subset for visualization\n", "    indices = np.random.choice(len(y_test), min(n_samples, len(y_test)), replace=False)\n", "    y_test_sample = y_test[indices]\n", "    y_pred_sample = y_pred[indices]\n", "    \n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # Scatter plot: Predicted vs Actual\n", "    axes[0, 0].scatter(y_test_sample, y_pred_sample, alpha=0.6, s=20)\n", "    axes[0, 0].plot([y_test_sample.min(), y_test_sample.max()], \n", "                    [y_test_sample.min(), y_test_sample.max()], 'r--', lw=2)\n", "    axes[0, 0].set_xlabel('Actual Values')\n", "    axes[0, 0].set_ylabel('Predicted Values')\n", "    axes[0, 0].set_title('Predicted vs Actual Values')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # Time series plot\n", "    sample_indices = np.arange(min(200, len(y_test)))\n", "    axes[0, 1].plot(sample_indices, y_test[:len(sample_indices)], label='Actual', alpha=0.8)\n", "    axes[0, 1].plot(sample_indices, y_pred[:len(sample_indices)], label='Predicted', alpha=0.8)\n", "    axes[0, 1].set_xlabel('Time Steps')\n", "    axes[0, 1].set_ylabel('Price Delta')\n", "    axes[0, 1].set_title('Time Series: Actual vs Predicted')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # Residuals plot\n", "    residuals = y_test - y_pred\n", "    axes[1, 0].scatter(y_pred, residuals, alpha=0.6, s=20)\n", "    axes[1, 0].axhline(y=0, color='r', linestyle='--')\n", "    axes[1, 0].set_xlabel('Predicted Values')\n", "    axes[1, 0].set_ylabel('Residuals')\n", "    axes[1, 0].set_title('Residuals Plot')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # Residuals histogram\n", "    axes[1, 1].hist(residuals, bins=50, alpha=0.7, edgecolor='black')\n", "    axes[1, 1].set_xlabel('Residuals')\n", "    axes[1, 1].set_ylabel('Frequency')\n", "    axes[1, 1].set_title('Residuals Distribution')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Plot predictions\n", "plot_predictions(y_test, predictions)"]}, {"cell_type": "code", "execution_count": null, "id": "model-saving", "metadata": {}, "outputs": [], "source": ["# Save the trained model and preprocessing objects\n", "import joblib\n", "import os\n", "\n", "# Create models directory if it doesn't exist\n", "os.makedirs('models', exist_ok=True)\n", "\n", "# Save the trained model\n", "model.save('models/xauusd_neural_model.h5')\n", "print(\"Model saved to: models/xauusd_neural_model.h5\")\n", "\n", "# Save the scaler\n", "joblib.dump(scaler, 'models/feature_scaler.pkl')\n", "print(\"Scaler saved to: models/feature_scaler.pkl\")\n", "\n", "# Save feature names\n", "joblib.dump(feature_names, 'models/feature_names.pkl')\n", "print(\"Feature names saved to: models/feature_names.pkl\")\n", "\n", "# Save model metadata\n", "metadata = {\n", "    'window_size': 20,\n", "    'prediction_horizon': 5,\n", "    'n_features': len(feature_names),\n", "    'input_shape': input_shape,\n", "    'model_metrics': metrics,\n", "    'training_samples': len(X_train),\n", "    'validation_samples': len(X_val),\n", "    'test_samples': len(X_test)\n", "}\n", "\n", "joblib.dump(metadata, 'models/model_metadata.pkl')\n", "print(\"Metadata saved to: models/model_metadata.pkl\")\n", "\n", "print(\"\\nAll model artifacts saved successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "prediction-function", "metadata": {}, "outputs": [], "source": ["# Create prediction function for real-time use\n", "def create_prediction_function():\n", "    \"\"\"\n", "    Create a function that can be used for real-time predictions\n", "    This function can be integrated into an MT5 EA\n", "    \"\"\"\n", "    def predict_price_movement(recent_data, model, scaler, feature_names, window_size=20):\n", "        \"\"\"\n", "        Predict price movement for the next 5 periods (50 minutes)\n", "        \n", "        Args:\n", "            recent_data: DataFrame with recent OHLCV data (at least window_size + buffer rows)\n", "            model: Trained neural network model\n", "            scaler: Fitted StandardScaler\n", "            feature_names: List of feature column names\n", "            window_size: Number of time steps to look back\n", "        \n", "        Returns:\n", "            Dictionary with prediction results\n", "        \"\"\"\n", "        try:\n", "            # Create features for recent data\n", "            data_with_features = create_features(recent_data)\n", "            \n", "            # Select only the required features\n", "            feature_data = data_with_features[feature_names].dropna()\n", "            \n", "            if len(feature_data) < window_size:\n", "                return {\n", "                    'error': f'Insufficient data. Need at least {window_size} rows, got {len(feature_data)}',\n", "                    'prediction': None,\n", "                    'confidence': None\n", "                }\n", "            \n", "            # Take the last window_size rows\n", "            recent_features = feature_data.iloc[-window_size:].values\n", "            \n", "            # Scale features\n", "            recent_features_scaled = scaler.transform(recent_features)\n", "            \n", "            # Reshape for model input\n", "            X_input = recent_features_scaled.reshape(1, window_size, -1)\n", "            \n", "            # Make prediction\n", "            prediction = model.predict(X_input, verbose=0)[0][0]\n", "            \n", "            # Calculate confidence based on recent model performance\n", "            # This is a simplified confidence measure\n", "            abs_prediction = abs(prediction)\n", "            confidence = min(abs_prediction / 2.0, 1.0)  # Normalize to 0-1\n", "            \n", "            # Determine trading signal\n", "            threshold = 0.5  # Can be adjusted based on backtesting\n", "            if prediction > threshold:\n", "                signal = 'BUY'\n", "            elif prediction < -threshold:\n", "                signal = 'SELL'\n", "            else:\n", "                signal = 'HOLD'\n", "            \n", "            return {\n", "                'prediction': float(prediction),\n", "                'signal': signal,\n", "                'confidence': float(confidence),\n", "                'threshold': threshold,\n", "                'timestamp': recent_data.index[-1] if hasattr(recent_data, 'index') else None,\n", "                'error': None\n", "            }\n", "            \n", "        except Exception as e:\n", "            return {\n", "                'error': str(e),\n", "                'prediction': None,\n", "                'signal': 'ERROR',\n", "                'confidence': None\n", "            }\n", "    \n", "    return predict_price_movement\n", "\n", "# Create the prediction function\n", "predict_price_movement = create_prediction_function()\n", "\n", "# Test the prediction function with recent data\n", "print(\"Testing prediction function with recent data...\")\n", "test_prediction = predict_price_movement(\n", "    data.iloc[-50:],  # Use last 50 rows for testing\n", "    model, \n", "    scaler, \n", "    feature_names\n", ")\n", "\n", "print(\"\\n=== Test Prediction Result ===\")\n", "for key, value in test_prediction.items():\n", "    print(f\"{key}: {value}\")"]}, {"cell_type": "markdown", "id": "integration-notes", "metadata": {}, "source": ["# Integration Notes for MT5 EA\n", "\n", "## Model Summary\n", "- **Architecture**: 1D CNN + Dense layers\n", "- **Input**: 20 time steps of technical indicators and price features\n", "- **Output**: Price delta prediction for next 5 periods (50 minutes)\n", "- **Features**: 40+ technical indicators including EMAs, RSI, MACD, ATR, Bollinger Bands\n", "\n", "## Trading Logic Implementation\n", "```python\n", "# Entry conditions (as per modelCreation.md)\n", "if prediction > threshold and price > ema_21 and atr > avg_atr:\n", "    # Open BUY position\n", "elif prediction < -threshold and price < ema_21 and atr > avg_atr:\n", "    # Open SELL position\n", "```\n", "\n", "## Files Created\n", "1. `models/xauusd_neural_model.h5` - Trained neural network\n", "2. `models/feature_scaler.pkl` - Feature normalization scaler\n", "3. `models/feature_names.pkl` - List of required features\n", "4. `models/model_metadata.pkl` - Model configuration and metrics\n", "\n", "## Next Steps\n", "1. **Backtesting**: Test the model on out-of-sample data\n", "2. **Parameter Optimization**: Fine-tune threshold values\n", "3. **Risk Management**: Implement stop-loss and take-profit logic\n", "4. **MT5 Integration**: Convert to ONNX format or use Python bridge\n", "5. **Live Testing**: Deploy on demo account first\n", "\n", "## Model Performance\n", "- Check the evaluation metrics above for model quality\n", "- Monitor directional accuracy and trading simulation results\n", "- Consider retraining if performance degrades over time"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}