//+------------------------------------------------------------------+
//| Get neural network prediction                                    |
//+------------------------------------------------------------------+
void GetNeuralPrediction()
{
   // Create fresh data file with latest bars
   if(!CreateDataFile(100))
   {
      Print("ERROR: Failed to create data file for prediction");
      return;
   }
   
   // Execute Python prediction script
   string command = PythonPath + " " + ScriptPath;
   int result = system(command);
   
   if(result != 0)
   {
      Print("ERROR: Python prediction failed. Return code: ", result);
      return;
   }
   
   // Read prediction result
   string predictionResult = ReadPredictionFile();
   if(predictionResult != "")
   {
      ParsePredictionResult(predictionResult);
      lastPredictionTime = TimeCurrent();
      Print("Neural prediction: ", lastPrediction, " Signal: ", lastSignal, " Confidence: ", lastConfidence);
   }
}

//+------------------------------------------------------------------+
//| Read prediction result from file                                 |
//+------------------------------------------------------------------+
string ReadPredictionFile()
{
   int fileHandle = FileOpen(predictionFileName, FILE_READ | FILE_TXT | FILE_ANSI);
   if(fileHandle == INVALID_HANDLE)
   {
      Print("ERROR: Failed to open prediction file");
      return "";
   }
   
   string result = "";
   if(!FileIsEnding(fileHandle))
   {
      result = FileReadString(fileHandle);
   }
   
   FileClose(fileHandle);
   return result;
}

//+------------------------------------------------------------------+
//| Parse prediction result string                                   |
//+------------------------------------------------------------------+
void ParsePredictionResult(string result)
{
   // Expected format: "SIGNAL:BUY,PREDICTION:0.5432,CONFIDENCE:1.8"
   string parts[];
   int count = StringSplit(result, ',', parts);
   
   for(int i = 0; i < count; i++)
   {
      string keyValue[];
      if(StringSplit(parts[i], ':', keyValue) == 2)
      {
         if(keyValue[0] == "SIGNAL")
         {
            lastSignal = keyValue[1];
         }
         else if(keyValue[0] == "PREDICTION")
         {
            lastPrediction = StringToDouble(keyValue[1]);
         }
         else if(keyValue[0] == "CONFIDENCE")
         {
            lastConfidence = StringToDouble(keyValue[1]);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Get technical analysis signal (fallback)                        |
//+------------------------------------------------------------------+
void GetTechnicalSignal()
{
   double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double ema = emaBuffer[0];
   double rsi = rsiBuffer[0];
   double atr = atrBuffer[0];
   double macdMain = macdMainBuffer[0];
   double macdSignal = macdSignalBuffer[0];
   
   // Simple technical analysis logic
   lastConfidence = 1.0; // Base confidence for technical signals
   
   // Trend analysis
   bool bullishTrend = (currentPrice > ema) && (macdMain > macdSignal);
   bool bearishTrend = (currentPrice < ema) && (macdMain < macdSignal);
   
   // RSI conditions
   bool rsiOversold = rsi < 30;
   bool rsiOverbought = rsi > 70;
   bool rsiNeutral = (rsi >= 40 && rsi <= 60);
   
   // Generate signal
   if(bullishTrend && !rsiOverbought && rsiNeutral)
   {
      lastSignal = "BUY";
      lastPrediction = atr * 0.5; // Predict positive movement
      lastConfidence = 1.2;
   }
   else if(bearishTrend && !rsiOversold && rsiNeutral)
   {
      lastSignal = "SELL";
      lastPrediction = -atr * 0.5; // Predict negative movement
      lastConfidence = 1.2;
   }
   else
   {
      lastSignal = "HOLD";
      lastPrediction = 0.0;
      lastConfidence = 0.5;
   }
   
   lastPredictionTime = TimeCurrent();
   Print("Technical signal: ", lastSignal, " Prediction: ", lastPrediction, " Confidence: ", lastConfidence);
}

//+------------------------------------------------------------------+
//| Execute trading logic                                            |
//+------------------------------------------------------------------+
void ExecuteTradingLogic()
{
   // Check if we have a valid signal
   if(lastSignal == "HOLD" || lastConfidence < ConfidenceThreshold)
      return;
   
   // Check position limits
   if(PositionsTotal() >= MaxPositions)
      return;
   
   // Check if we already have a position
   if(position.Select(Symbol()))
   {
      // We have a position, check for exit conditions
      CheckExitConditions();
      return;
   }
   
   // Calculate position size
   double lotSize = CalculateLotSize();
   if(lotSize <= 0)
      return;
   
   // Calculate stop loss and take profit
   double stopLoss = CalculateStopLoss();
   double takeProfit = CalculateTakeProfit();
   
   // Execute trade based on signal
   if(lastSignal == "BUY" && EnableLongTrades)
   {
      ExecuteBuyOrder(lotSize, stopLoss, takeProfit);
   }
   else if(lastSignal == "SELL" && EnableShortTrades)
   {
      ExecuteSellOrder(lotSize, stopLoss, takeProfit);
   }
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk                           |
//+------------------------------------------------------------------+
double CalculateLotSize()
{
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * RiskPercent / 100.0;
   
   double stopLossPoints = CalculateStopLossPoints();
   if(stopLossPoints <= 0)
      return 0;
   
   double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
   double lotSize = riskAmount / (stopLossPoints * tickValue);
   
   // Apply lot size limits
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
   
   lotSize = MathMax(lotSize, minLot);
   lotSize = MathMin(lotSize, maxLot);
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;
   
   return lotSize;
}

//+------------------------------------------------------------------+
//| Calculate stop loss in points                                   |
//+------------------------------------------------------------------+
double CalculateStopLossPoints()
{
   double atr = atrBuffer[0];
   double stopLossPoints = atr * StopLossATRMultiplier * MathPow(10, Digits());
   
   stopLossPoints = MathMax(stopLossPoints, MinStopLoss);
   stopLossPoints = MathMin(stopLossPoints, MaxStopLoss);
   
   return stopLossPoints;
}

//+------------------------------------------------------------------+
//| Calculate stop loss price                                        |
//+------------------------------------------------------------------+
double CalculateStopLoss()
{
   double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double stopLossPoints = CalculateStopLossPoints();
   double pointValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   
   if(lastSignal == "BUY")
   {
      return currentPrice - (stopLossPoints * pointValue);
   }
   else if(lastSignal == "SELL")
   {
      return currentPrice + (stopLossPoints * pointValue);
   }
   
   return 0;
}

//+------------------------------------------------------------------+
//| Calculate take profit price                                      |
//+------------------------------------------------------------------+
double CalculateTakeProfit()
{
   double currentPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double atr = atrBuffer[0];
   double takeProfitPoints = atr * TakeProfitATRMultiplier * MathPow(10, Digits());
   double pointValue = SymbolInfoDouble(Symbol(), SYMBOL_POINT);
   
   if(lastSignal == "BUY")
   {
      return currentPrice + (takeProfitPoints * pointValue);
   }
   else if(lastSignal == "SELL")
   {
      return currentPrice - (takeProfitPoints * pointValue);
   }
   
   return 0;
}

//+------------------------------------------------------------------+
//| Execute buy order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder(double lotSize, double stopLoss, double takeProfit)
{
   double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
   
   if(trade.Buy(lotSize, Symbol(), price, stopLoss, takeProfit, TradeComment))
   {
      Print("BUY order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
      lastTradeTime = TimeCurrent();
   }
   else
   {
      Print("ERROR: Failed to execute BUY order. Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| Execute sell order                                               |
//+------------------------------------------------------------------+
void ExecuteSellOrder(double lotSize, double stopLoss, double takeProfit)
{
   double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   
   if(trade.Sell(lotSize, Symbol(), price, stopLoss, takeProfit, TradeComment))
   {
      Print("SELL order executed: Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
      lastTradeTime = TimeCurrent();
   }
   else
   {
      Print("ERROR: Failed to execute SELL order. Error: ", GetLastError());
   }
}
